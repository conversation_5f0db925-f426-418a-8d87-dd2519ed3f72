import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function ChargingScreen() {
  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Current Charging Status */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
        <Text className="text-xl font-bold text-gray-900 mb-4">Charging Status</Text>
        
        <View className="bg-electric-50 rounded-lg p-4 mb-4">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-lg font-semibold text-gray-900">Not Charging</Text>
            <View className="bg-gray-200 rounded-full px-3 py-1">
              <Text className="text-sm font-medium text-gray-700">Disconnected</Text>
            </View>
          </View>
          <Text className="text-sm text-gray-600">Connect to a charging station to begin</Text>
        </View>

        <TouchableOpacity className="bg-electric-600 rounded-lg p-4 items-center">
          <View className="flex-row items-center">
            <Ionicons name="map" size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Find Nearby Stations</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Nearby Charging Stations */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Nearby Stations</Text>
        
        <View className="bg-white rounded-xl shadow-sm">
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="font-medium text-gray-900">Tesla Supercharger</Text>
                <Text className="text-sm text-gray-600">Downtown Mall - 0.3 miles</Text>
                <View className="flex-row items-center mt-1">
                  <View className="bg-green-100 rounded-full px-2 py-1 mr-2">
                    <Text className="text-xs font-medium text-green-800">Available</Text>
                  </View>
                  <Text className="text-sm text-gray-600">8/12 stalls</Text>
                </View>
              </View>
              <View className="items-end">
                <Text className="font-semibold text-gray-900">$0.28/kWh</Text>
                <Text className="text-sm text-gray-600">250kW</Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="font-medium text-gray-900">ChargePoint</Text>
                <Text className="text-sm text-gray-600">City Center - 0.7 miles</Text>
                <View className="flex-row items-center mt-1">
                  <View className="bg-yellow-100 rounded-full px-2 py-1 mr-2">
                    <Text className="text-xs font-medium text-yellow-800">Busy</Text>
                  </View>
                  <Text className="text-sm text-gray-600">2/6 stalls</Text>
                </View>
              </View>
              <View className="items-end">
                <Text className="font-semibold text-gray-900">$0.32/kWh</Text>
                <Text className="text-sm text-gray-600">150kW</Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4">
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="font-medium text-gray-900">Electrify America</Text>
                <Text className="text-sm text-gray-600">Highway Plaza - 1.2 miles</Text>
                <View className="flex-row items-center mt-1">
                  <View className="bg-green-100 rounded-full px-2 py-1 mr-2">
                    <Text className="text-xs font-medium text-green-800">Available</Text>
                  </View>
                  <Text className="text-sm text-gray-600">6/8 stalls</Text>
                </View>
              </View>
              <View className="items-end">
                <Text className="font-semibold text-gray-900">$0.35/kWh</Text>
                <Text className="text-sm text-gray-600">350kW</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Charging History */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Recent Sessions</Text>
        
        <View className="bg-white rounded-xl shadow-sm">
          <View className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Tesla Supercharger</Text>
              <Text className="text-sm text-gray-600">Yesterday</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="text-sm text-gray-600">45 kWh • 2h 15m</Text>
              <Text className="font-semibold text-gray-900">$12.60</Text>
            </View>
          </View>
          
          <View className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">ChargePoint</Text>
              <Text className="text-sm text-gray-600">3 days ago</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="text-sm text-gray-600">32 kWh • 3h 45m</Text>
              <Text className="font-semibold text-gray-900">$10.24</Text>
            </View>
          </View>
          
          <View className="p-4">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Home Charging</Text>
              <Text className="text-sm text-gray-600">1 week ago</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="text-sm text-gray-600">68 kWh • 8h 30m</Text>
              <Text className="font-semibold text-gray-900">$8.16</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Bottom Spacing */}
      <View className="h-6" />
    </ScrollView>
  );
}
