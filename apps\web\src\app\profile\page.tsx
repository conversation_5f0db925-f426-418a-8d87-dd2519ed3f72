'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { showToast } from '@/lib/toast'
import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Header } from '@/components/Header'
import { AvatarUpload } from '@/components/AvatarUpload'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { profileApi } from '@/lib/api'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Save,
  Edit3,
  Shield,
  Bell,
  CreditCard,
  Download,
} from 'lucide-react'

function ProfileContent() {
  const { user, profile, updateProfile, refreshProfile } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    full_name: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    country: 'US',
  })
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [userStats, setUserStats] = useState({
    vehicleCount: 0,
    chargingSessionCount: 0,
    totalMiles: 0,
  })

  // Update form data when profile changes
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        zip_code: profile.zip_code || '',
        country: profile.country || 'US',
      })
    }
  }, [profile])

  // Load user statistics
  useEffect(() => {
    const loadUserStats = async () => {
      const { data } = await profileApi.getUserStats()
      if (data) {
        setUserStats(data)
      }
    }

    if (user) {
      loadUserStats()
    }
  }, [user])

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setLoading(true)
    setError(null)

    // Show loading toast
    const loadingToast = showToast.electric.loading('Updating profile...')

    try {
      // Handle avatar upload first if there's a new file
      if (avatarFile) {
        const { error: avatarError } = await profileApi.uploadAvatar(avatarFile)
        if (avatarError) {
          throw new Error('Failed to upload avatar')
        }
      }

      // Update profile data
      const { error: updateError } = await updateProfile(formData)
      if (updateError) {
        throw new Error('Failed to update profile')
      }

      // Refresh profile data
      await refreshProfile()

      // Success toast
      showToast.electric.success('Profile updated successfully!', {
        id: loadingToast,
      })

      setIsEditing(false)
      setAvatarFile(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)

      // Error toast
      showToast.electric.error(errorMessage, {
        id: loadingToast,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAvatarChange = async (file: File | null) => {
    if (file === null && profile?.avatar_url) {
      // Handle avatar removal
      const loadingToast = showToast.electric.loading('Removing avatar...')

      try {
        setLoading(true)
        const { error } = await profileApi.deleteAvatar()
        if (error) {
          const errorMessage = 'Failed to remove avatar'
          setError(errorMessage)
          showToast.electric.error(errorMessage, { id: loadingToast })
        } else {
          await refreshProfile()
          showToast.electric.success('Avatar removed successfully!', { id: loadingToast })
        }
      } catch (err) {
        const errorMessage = 'Failed to remove avatar'
        setError(errorMessage)
        showToast.electric.error(errorMessage, { id: loadingToast })
      } finally {
        setLoading(false)
      }
    } else {
      setAvatarFile(file)
      if (file) {
        showToast.info('Avatar selected! Click Save to upload.')
      }
    }
  }

  const handleCancel = () => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        zip_code: profile.zip_code || '',
        country: profile.country || 'US',
      })
    }
    setAvatarFile(null)
    setError(null)
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header variant="dashboard" />

      {/* Main Content */}
      <main className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your personal information and account preferences
          </p>
          {error && (
            <div className="mt-4 rounded-md bg-red-50 p-4 dark:bg-red-900/20">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Profile Overview Card */}
          <div className="lg:col-span-1">
            <Card className="dark:border-gray-700 dark:bg-gray-800">
              <CardHeader className="text-center">
                <div className="mb-4">
                  <AvatarUpload
                    currentAvatar={profile?.avatar_url || undefined}
                    onAvatarChange={handleAvatarChange}
                    size="md"
                    editable={isEditing}
                  />
                </div>
                <CardTitle className="dark:text-white">
                  {formData.full_name || user?.email?.split('@')[0] || 'User Name'}
                </CardTitle>
                <CardDescription className="dark:text-gray-300">{user?.email}</CardDescription>
                <Badge
                  variant="secondary"
                  className="mt-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                >
                  Verified Account
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span>
                    Joined{' '}
                    {profile?.created_at
                      ? new Date(profile.created_at).toLocaleDateString()
                      : 'Recently'}
                  </span>
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <MapPin className="mr-2 h-4 w-4" />
                  <span>
                    {formData.city && formData.state
                      ? `${formData.city}, ${formData.state}`
                      : formData.address || 'No location provided'}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="mt-6 dark:border-gray-700 dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="text-lg dark:text-white">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                  asChild
                >
                  <Link href="/settings">
                    <Shield className="mr-2 h-4 w-4" />
                    Security Settings
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  Billing & Plans
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export Data
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Profile Details Form */}
          <div className="lg:col-span-2">
            <Card className="dark:border-gray-700 dark:bg-gray-800">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="dark:text-white">Personal Information</CardTitle>
                  <CardDescription className="dark:text-gray-300">
                    Update your personal details and contact information
                  </CardDescription>
                </div>
                {!isEditing ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="dark:border-gray-600 dark:text-gray-300"
                  >
                    <Edit3 className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancel}
                      className="dark:border-gray-600 dark:text-gray-300"
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      disabled={loading}
                      className="bg-electric-600 hover:bg-electric-700"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {loading ? 'Saving...' : 'Save'}
                    </Button>
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="full_name" className="dark:text-gray-300">
                      Full Name
                    </Label>
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Enter your full name"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="dark:text-gray-300">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={user?.email || ''}
                      disabled={true}
                      className="opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Email cannot be changed here. Contact support if needed.
                    </p>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="dark:text-gray-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      disabled={!isEditing}
                      placeholder="+****************"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country" className="dark:text-gray-300">
                      Country
                    </Label>
                    <select
                      id="country"
                      value={formData.country}
                      onChange={(e) => handleInputChange('country', e.target.value)}
                      disabled={!isEditing}
                      className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="GB">United Kingdom</option>
                      <option value="DE">Germany</option>
                      <option value="FR">France</option>
                      <option value="AU">Australia</option>
                      <option value="JP">Japan</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address" className="dark:text-gray-300">
                    Street Address
                  </Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    disabled={!isEditing}
                    placeholder="123 Main Street"
                    className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="city" className="dark:text-gray-300">
                      City
                    </Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      disabled={!isEditing}
                      placeholder="San Francisco"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state" className="dark:text-gray-300">
                      State/Province
                    </Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      disabled={!isEditing}
                      placeholder="CA"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="zip_code" className="dark:text-gray-300">
                      ZIP/Postal Code
                    </Label>
                    <Input
                      id="zip_code"
                      value={formData.zip_code}
                      onChange={(e) => handleInputChange('zip_code', e.target.value)}
                      disabled={!isEditing}
                      placeholder="94102"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Statistics */}
            <Card className="mt-6 dark:border-gray-700 dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="dark:text-white">Account Statistics</CardTitle>
                <CardDescription className="dark:text-gray-300">
                  Your activity and usage overview
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-electric-600">
                      {userStats.vehicleCount}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Vehicle{userStats.vehicleCount !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {userStats.chargingSessionCount}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Charging Sessions</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {userStats.totalMiles.toLocaleString()}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Miles Driven</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <ProfileContent />
    </ProtectedRoute>
  )
}
