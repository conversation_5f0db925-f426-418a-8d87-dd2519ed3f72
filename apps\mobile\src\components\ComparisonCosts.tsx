import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { EVModel } from '@greenmiles-ev/shared';

interface ComparisonCostsProps {
  models: EVModel[];
}

export function ComparisonCosts({ models }: ComparisonCostsProps) {
  const [milesPerYear, setMilesPerYear] = useState(12000);
  const [electricityRate, setElectricityRate] = useState(0.13);
  const [gasPrice, setGasPrice] = useState(3.50);
  const [loanTerm, setLoanTerm] = useState(60);
  const [downPayment, setDownPayment] = useState(20);
  const [interestRate, setInterestRate] = useState(4.5);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const calculateCosts = (model: EVModel) => {
    const basePrice = model.price_msrp ? model.price_msrp / 100 : 0;
    
    // Financing calculations
    const downPaymentAmount = basePrice * (downPayment / 100);
    const loanAmount = basePrice - downPaymentAmount;
    const monthlyRate = interestRate / 100 / 12;
    const monthlyPayment = loanAmount > 0 
      ? (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, loanTerm)) / 
        (Math.pow(1 + monthlyRate, loanTerm) - 1)
      : 0;

    // Energy costs
    const annualEnergyConsumption = model.efficiency_mpge 
      ? (milesPerYear / model.efficiency_mpge) * 33.7 // kWh equivalent
      : 0;
    const annualElectricityCost = annualEnergyConsumption * electricityRate;

    // Gas car comparison
    const averageGasMPG = 28;
    const annualGasConsumption = milesPerYear / averageGasMPG;
    const annualGasCost = annualGasConsumption * gasPrice;
    const annualFuelSavings = annualGasCost - annualElectricityCost;

    // 5-year totals
    const fiveYearFinancing = monthlyPayment * Math.min(loanTerm, 60);
    const fiveYearElectricity = annualElectricityCost * 5;
    const fiveYearMaintenance = 2500; // Estimated EV maintenance
    const fiveYearInsurance = 6000; // Estimated insurance
    const fiveYearTotal = fiveYearFinancing + fiveYearElectricity + fiveYearMaintenance + fiveYearInsurance;

    return {
      basePrice,
      downPaymentAmount,
      monthlyPayment,
      annualElectricityCost,
      annualFuelSavings,
      fiveYearTotal,
      fiveYearElectricity,
      fiveYearMaintenance,
      fiveYearInsurance,
    };
  };

  const getBestValue = (costs: ReturnType<typeof calculateCosts>[]) => {
    return costs.reduce((best, current, index) => {
      if (current.fiveYearTotal < best.cost) {
        return { cost: current.fiveYearTotal, index };
      }
      return best;
    }, { cost: Infinity, index: -1 });
  };

  const modelCosts = models.map(calculateCosts);
  const bestValue = getBestValue(modelCosts);

  const CostCard = ({ 
    title, 
    amount, 
    subtitle, 
    isBest = false 
  }: {
    title: string;
    amount: number;
    subtitle?: string;
    isBest?: boolean;
  }) => (
    <View className={`rounded-lg p-3 flex-1 ${isBest ? 'bg-green-50 border border-green-200' : 'bg-gray-50'}`}>
      <Text className="text-sm text-gray-600 mb-1">{title}</Text>
      <Text className={`text-lg font-bold ${isBest ? 'text-green-700' : 'text-gray-900'}`}>
        {formatCurrency(amount)}
      </Text>
      {subtitle && (
        <Text className="text-xs text-gray-500 mt-1">{subtitle}</Text>
      )}
      {isBest && (
        <View className="flex-row items-center mt-1">
          <Ionicons name="trophy" size={12} color="#16a34a" />
          <Text className="text-xs text-green-600 ml-1">Best Value</Text>
        </View>
      )}
    </View>
  );

  const ModelCostSection = ({ model, costs, index }: { 
    model: EVModel; 
    costs: ReturnType<typeof calculateCosts>; 
    index: number;
  }) => {
    const isBestValue = index === bestValue.index;

    return (
      <View className={`bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm ${
        isBestValue ? 'border-2 border-green-200' : ''
      }`}>
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-lg font-semibold text-gray-900">
              {model.make} {model.model}
            </Text>
            {model.trim && (
              <Text className="text-sm text-gray-600">{model.trim}</Text>
            )}
          </View>
          {isBestValue && (
            <View className="bg-green-100 rounded-full px-3 py-1">
              <Text className="text-green-700 text-sm font-medium">Best Value</Text>
            </View>
          )}
        </View>

        {/* Purchase Price */}
        <View className="mb-4">
          <Text className="text-base font-medium text-gray-900 mb-2">Purchase</Text>
          <View className="flex-row gap-3">
            <CostCard
              title="MSRP"
              amount={costs.basePrice}
            />
            <CostCard
              title="Monthly Payment"
              amount={costs.monthlyPayment}
              subtitle={`${loanTerm} months`}
            />
          </View>
        </View>

        {/* Operating Costs */}
        <View className="mb-4">
          <Text className="text-base font-medium text-gray-900 mb-2">Annual Operating</Text>
          <View className="flex-row gap-3">
            <CostCard
              title="Electricity"
              amount={costs.annualElectricityCost}
              subtitle={`${milesPerYear.toLocaleString()} mi/year`}
            />
            <CostCard
              title="Fuel Savings"
              amount={costs.annualFuelSavings}
              subtitle="vs. gas car"
            />
          </View>
        </View>

        {/* 5-Year Total */}
        <View className="bg-gray-50 rounded-lg p-3">
          <Text className="text-sm text-gray-600 mb-1">5-Year Total Cost</Text>
          <Text className={`text-2xl font-bold ${isBestValue ? 'text-green-600' : 'text-gray-900'}`}>
            {formatCurrency(costs.fiveYearTotal)}
          </Text>
          <View className="flex-row justify-between mt-2 text-xs text-gray-500">
            <Text>Financing: {formatCurrency(costs.fiveYearFinancing)}</Text>
            <Text>Energy: {formatCurrency(costs.fiveYearElectricity)}</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Cost Calculator Settings */}
      <View className="bg-white m-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Cost Calculator Settings</Text>
        
        {/* Annual Mileage */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Annual Mileage</Text>
            <Text className="font-medium text-gray-900">{milesPerYear.toLocaleString()} miles</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={5000}
            maximumValue={25000}
            step={1000}
            value={milesPerYear}
            onValueChange={setMilesPerYear}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Electricity Rate */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Electricity Rate</Text>
            <Text className="font-medium text-gray-900">${electricityRate.toFixed(2)}/kWh</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={0.08}
            maximumValue={0.30}
            step={0.01}
            value={electricityRate}
            onValueChange={setElectricityRate}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Financing Terms */}
        <View className="border-t border-gray-200 pt-4">
          <Text className="font-medium text-gray-900 mb-3">Financing Terms</Text>
          
          <View className="flex-row gap-4 mb-3">
            <View className="flex-1">
              <Text className="text-sm text-gray-600 mb-1">Down Payment</Text>
              <Text className="font-medium text-gray-900">{downPayment}%</Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-gray-600 mb-1">Loan Term</Text>
              <Text className="font-medium text-gray-900">{loanTerm} months</Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-gray-600 mb-1">Interest Rate</Text>
              <Text className="font-medium text-gray-900">{interestRate.toFixed(1)}%</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Cost Comparison Summary */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">5-Year Cost Comparison</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row gap-3">
            {models.map((model, index) => {
              const costs = modelCosts[index];
              const isBest = index === bestValue.index;
              
              return (
                <View 
                  key={model.id} 
                  className={`w-40 rounded-lg p-3 ${
                    isBest ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                  }`}
                >
                  <Text className="font-semibold text-gray-900 text-sm mb-1" numberOfLines={2}>
                    {model.make} {model.model}
                  </Text>
                  <Text className={`text-xl font-bold ${isBest ? 'text-green-600' : 'text-gray-900'}`}>
                    {formatCurrency(costs.fiveYearTotal)}
                  </Text>
                  {isBest && (
                    <View className="flex-row items-center mt-1">
                      <Ionicons name="trophy" size={12} color="#16a34a" />
                      <Text className="text-xs text-green-600 ml-1">Best Value</Text>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>

      {/* Detailed Cost Breakdown */}
      <View className="pb-20">
        <View className="px-4 mb-4">
          <Text className="text-lg font-semibold text-gray-900">Detailed Cost Analysis</Text>
          <Text className="text-gray-600 text-sm">
            Complete breakdown of ownership costs over 5 years
          </Text>
        </View>
        
        {models.map((model, index) => (
          <ModelCostSection
            key={model.id}
            model={model}
            costs={modelCosts[index]}
            index={index}
          />
        ))}
      </View>
    </ScrollView>
  );
}
