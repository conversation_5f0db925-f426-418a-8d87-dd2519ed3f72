import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { EVModel } from '@greenmiles-ev/shared';
import { useEVModels } from '../src/hooks/useEVModels';
import { useComparison } from '../src/contexts/ComparisonContext';
import { EVModelCard } from '../src/components/EVModelCard';
import { SearchFilters } from '../src/components/SearchFilters';
import { QuickFilters } from '../src/components/QuickFilters';

export default function BrowseScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [refreshing, setRefreshing] = useState(false);

  const {
    evModels,
    loading,
    error,
    filters,
    updateFilters,
    clearFilters,
    hasActiveFilters,
    refetch,
  } = useEVModels();

  const { comparisonCount, addToComparison, removeFromComparison, isInComparison } = useComparison();

  useEffect(() => {
    refetch();
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    updateFilters({ search: query });
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleModelPress = (model: EVModel) => {
    router.push(`/ev-models/${model.id}`);
  };

  const handleCompareToggle = (model: EVModel) => {
    if (isInComparison(model.id)) {
      removeFromComparison(model.id);
    } else {
      const success = addToComparison(model);
      if (!success) {
        Alert.alert(
          'Comparison Limit',
          'You can compare up to 4 vehicles at once. Remove one to add another.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  const handleViewComparison = () => {
    router.push('/comparison');
  };

  const renderEVModel = ({ item }: { item: EVModel }) => (
    <EVModelCard
      model={item}
      viewMode={viewMode}
      onPress={() => handleModelPress(item)}
      onCompareToggle={() => handleCompareToggle(item)}
      isInComparison={isInComparison(item.id)}
    />
  );

  const renderHeader = () => (
    <View className="bg-white">
      {/* Hero Section */}
      <View className="bg-gradient-to-r from-electric-600 to-electric-700 px-4 pt-12 pb-6">
        <Text className="text-2xl font-bold text-white mb-2">
          Find Your Perfect EV
        </Text>
        <Text className="text-electric-100 text-base">
          Discover and compare electric vehicles
        </Text>
      </View>

      {/* Search Bar */}
      <View className="px-4 py-4 bg-white shadow-sm">
        <View className="flex-row items-center bg-gray-100 rounded-xl px-4 py-3">
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            className="flex-1 ml-3 text-base"
            placeholder="Search by make, model, or features..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#6b7280"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Quick Filters */}
      <QuickFilters
        onFilterSelect={(filter) => updateFilters(filter)}
        activeFilters={filters}
      />

      {/* Filter Controls */}
      <View className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-200">
        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => setShowFilters(!showFilters)}
            className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2 mr-3"
          >
            <Ionicons name="options" size={18} color="#374151" />
            <Text className="ml-2 text-gray-700 font-medium">Filters</Text>
            {hasActiveFilters && (
              <View className="ml-2 bg-electric-600 rounded-full w-2 h-2" />
            )}
          </TouchableOpacity>

          {hasActiveFilters && (
            <TouchableOpacity
              onPress={clearFilters}
              className="flex-row items-center"
            >
              <Text className="text-electric-600 text-sm font-medium">Clear All</Text>
            </TouchableOpacity>
          )}
        </View>

        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="p-2"
          >
            <Ionicons
              name={viewMode === 'grid' ? 'list' : 'grid'}
              size={20}
              color="#374151"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Results Count */}
      <View className="px-4 py-2 bg-gray-50">
        <Text className="text-sm text-gray-600">
          {evModels?.length || 0} vehicles found
        </Text>
      </View>
    </View>
  );

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Ionicons name="alert-circle" size={48} color="#ef4444" />
        <Text className="text-lg font-semibold text-gray-900 mt-4 mb-2">
          Something went wrong
        </Text>
        <Text className="text-gray-600 text-center px-8 mb-6">
          We couldn't load the EV models. Please try again.
        </Text>
        <TouchableOpacity
          onPress={refetch}
          className="bg-electric-600 rounded-lg px-6 py-3"
        >
          <Text className="text-white font-semibold">Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      {/* Filters Modal */}
      {showFilters && (
        <SearchFilters
          filters={filters}
          onFiltersChange={updateFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Main Content */}
      <FlatList
        data={evModels || []}
        renderItem={renderEVModel}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={{
          paddingBottom: comparisonCount > 0 ? 100 : 20,
        }}
        columnWrapperStyle={viewMode === 'grid' ? { paddingHorizontal: 16, gap: 12 } : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#22c55e']}
            tintColor="#22c55e"
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Comparison Bar */}
      {comparisonCount > 0 && (
        <View className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
          <TouchableOpacity
            onPress={handleViewComparison}
            className="bg-electric-600 rounded-xl p-4 flex-row items-center justify-between"
          >
            <View className="flex-row items-center">
              <Ionicons name="analytics" size={24} color="white" />
              <Text className="text-white font-semibold ml-3">
                Compare {comparisonCount} Vehicle{comparisonCount > 1 ? 's' : ''}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {/* Loading Overlay */}
      {loading && (
        <View className="absolute inset-0 bg-black/20 justify-center items-center">
          <View className="bg-white rounded-xl p-6 items-center">
            <Text className="text-gray-600 mt-2">Loading vehicles...</Text>
          </View>
        </View>
      )}
    </View>
  );
}
