# Mobile EV Browser - React Native

## Overview

The Mobile EV Browser is a React Native application built with Expo that provides a touch-optimized interface for browsing, comparing, and researching electric vehicles. It's designed specifically for mobile users who want to explore EV options on-the-go.

## Features

### 🔍 **Smart Search & Browse**
- **Touch-optimized search** with intelligent suggestions
- **Quick filters** for common buyer needs (Under $40k, 300+ Miles, etc.)
- **Advanced filtering** with sliders and multi-select options
- **Grid and list view modes** for different browsing preferences

### 📱 **Mobile-First Design**
- **Responsive layouts** optimized for phones and tablets
- **Touch-friendly interactions** with proper tap targets
- **Swipe gestures** for navigation and actions
- **Native performance** with smooth scrolling and animations

### 🔄 **Vehicle Comparison**
- **Add up to 4 vehicles** to comparison list
- **Persistent comparison state** across app sessions
- **Side-by-side specifications** comparison
- **Visual comparison tables** with key metrics
- **Quick comparison access** from floating action bar

### 📊 **Detailed Vehicle Information**
- **High-resolution vehicle images** with fallback placeholders
- **Comprehensive specifications** organized by categories
- **Key metrics highlighting** (Range, Efficiency, Performance)
- **Manufacturer information** and branding
- **Badge system** for Featured, Editor's Choice, Best Value

### 🎯 **Decision Support Tools**
- **Quick filter categories** for different buyer profiles
- **Price range sliders** with real-time updates
- **Performance filtering** (acceleration, range, efficiency)
- **Body type selection** for different needs
- **Special category filters** (luxury, affordable, family-friendly)

## Technical Architecture

### **Framework & Tools**
- **Expo SDK 50** - React Native development platform
- **React Navigation 6** - Native navigation with tab and stack navigators
- **NativeWind** - Tailwind CSS for React Native styling
- **TypeScript** - Type-safe development
- **Supabase** - Backend database and real-time features

### **Key Components**

#### **Browse Screen** (`apps/mobile/app/(tabs)/browse.tsx`)
- Main EV browsing interface
- Search and filtering functionality
- Grid/list view toggle
- Comparison management

#### **EV Model Card** (`apps/mobile/src/components/EVModelCard.tsx`)
- Reusable vehicle display component
- Supports both grid and list layouts
- Integrated comparison actions
- Badge and rating display

#### **Search Filters** (`apps/mobile/src/components/SearchFilters.tsx`)
- Advanced filtering modal
- Range sliders for price, range, efficiency
- Multi-select options for make, body type
- Real-time filter application

#### **Quick Filters** (`apps/mobile/src/components/QuickFilters.tsx`)
- Horizontal scrolling filter chips
- Pre-defined buyer scenarios
- One-tap filter application
- Visual active state indicators

#### **Comparison Context** (`apps/mobile/src/contexts/ComparisonContext.tsx`)
- Global state management for vehicle comparisons
- AsyncStorage persistence
- Maximum comparison limits
- Cross-screen comparison access

### **Data Management**

#### **useEVModels Hook** (`apps/mobile/src/hooks/useEVModels.ts`)
- Centralized data fetching and state management
- Real-time filter application
- Loading and error states
- Optimized queries with proper indexing

#### **Supabase Integration** (`apps/mobile/src/lib/supabase.ts`)
- Type-safe database queries
- Real-time subscriptions (future feature)
- Optimized data fetching with joins
- Error handling and retry logic

## Setup Instructions

### **Prerequisites**
- Node.js 18+ and npm/yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Emulator
- Expo Go app for physical device testing

### **Installation**
```bash
# Navigate to mobile app directory
cd apps/mobile

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit .env with your Supabase credentials
# EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
# EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### **Development**
```bash
# Start Expo development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android

# Run on physical device (scan QR code with Expo Go)
npm start
```

### **Building for Production**
```bash
# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

## User Experience Features

### **Performance Optimizations**
- **Lazy loading** for images and components
- **Virtualized lists** for large datasets
- **Debounced search** to reduce API calls
- **Optimistic updates** for better perceived performance
- **Cached data** with AsyncStorage

### **Accessibility**
- **Screen reader support** with proper labels
- **High contrast mode** compatibility
- **Large text support** for accessibility
- **Touch target sizing** following platform guidelines
- **Keyboard navigation** support

### **Offline Capabilities**
- **Cached vehicle data** for offline browsing
- **Comparison persistence** without network
- **Graceful degradation** when offline
- **Sync on reconnection** for updated data

## Integration with Web Platform

### **Shared Components**
- **Type definitions** shared via `@greenmiles-ev/shared` package
- **Utility functions** for formatting and calculations
- **Constants** for vehicle makes, body types, etc.
- **Database schema** consistency across platforms

### **Data Synchronization**
- **Real-time updates** via Supabase subscriptions
- **Consistent filtering** logic across web and mobile
- **Shared comparison algorithms** and scoring
- **Cross-platform user preferences** (future feature)

## Future Enhancements

### **Planned Features**
- **User accounts** with saved searches and favorites
- **Push notifications** for new vehicles and price changes
- **Augmented reality** vehicle visualization
- **Location-based** dealer and charging station integration
- **Social features** for sharing and reviews
- **Advanced AI recommendations** based on usage patterns

### **Technical Improvements**
- **Offline-first architecture** with sync capabilities
- **Advanced caching strategies** for better performance
- **Machine learning** for personalized recommendations
- **Real-time collaboration** features
- **Advanced analytics** and user behavior tracking

## Testing

### **Unit Tests**
```bash
# Run unit tests
npm test

# Run tests with coverage
npm run test:coverage
```

### **E2E Testing**
```bash
# Run Detox E2E tests (requires setup)
npm run test:e2e
```

### **Manual Testing Checklist**
- [ ] Search functionality works across different queries
- [ ] Filters apply correctly and persist
- [ ] Comparison adds/removes vehicles properly
- [ ] Navigation between screens is smooth
- [ ] Images load correctly with fallbacks
- [ ] Offline functionality works as expected
- [ ] Performance is smooth on target devices

## Troubleshooting

### **Common Issues**
- **Metro bundler issues**: Clear cache with `npx expo start --clear`
- **iOS simulator problems**: Reset simulator and rebuild
- **Android emulator issues**: Ensure proper AVD configuration
- **Supabase connection**: Verify environment variables and network

### **Performance Issues**
- **Slow scrolling**: Check for unnecessary re-renders
- **Memory leaks**: Verify proper cleanup in useEffect hooks
- **Large bundle size**: Analyze with `npx expo export --dump-sourcemap`

---

*For more detailed technical documentation, see the individual component files and the shared package documentation.*
