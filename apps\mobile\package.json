{"name": "@greenmiles-ev/mobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "build:android": "expo build:android", "build:ios": "expo build:ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/slider": "^4.4.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.38.5", "expo": "~50.0.0", "expo-constants": "~15.4.0", "expo-font": "~11.10.0", "expo-linking": "~6.2.0", "expo-router": "~3.4.0", "expo-splash-screen": "~0.26.0", "expo-status-bar": "~1.11.1", "nativewind": "^2.0.11", "react": "18.2.0", "react-native": "^0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.23.0", "@testing-library/react-native": "^12.4.0", "@types/react": "~18.2.45", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-expo": "^7.0.0", "jest": "^29.7.0", "prettier": "^3.1.0", "tailwindcss": "3.3.2", "typescript": "^5.3.0"}, "private": true}