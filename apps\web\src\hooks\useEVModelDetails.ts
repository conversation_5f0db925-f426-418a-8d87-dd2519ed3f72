'use client'

import { useState, useEffect, useCallback } from 'react'
import type { EVModelWithDetails } from '@/shared/types'

interface UseEVModelDetailsReturn {
  evModel: EVModelWithDetails | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useEVModelDetails(id: string): UseEVModelDetailsReturn {
  const [evModel, setEvModel] = useState<EVModelWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEVModelDetails = useCallback(async () => {
    if (!id) {
      setError('Invalid EV model ID')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/ev-models/${id}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('EV model not found')
        }
        throw new Error(`Failed to fetch EV model: ${response.status}`)
      }

      const data = await response.json()
      
      if (!data.data) {
        throw new Error('Invalid response format')
      }

      setEvModel(data.data)
    } catch (err) {
      console.error('Error fetching EV model details:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch EV model details')
      setEvModel(null)
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    fetchEVModelDetails()
  }, [fetchEVModelDetails])

  return {
    evModel,
    loading,
    error,
    refetch: fetchEVModelDetails
  }
}
