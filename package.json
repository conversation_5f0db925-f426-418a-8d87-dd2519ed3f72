{"name": "greenmiles-ev", "version": "1.0.0", "description": "GreenMilesEV - Full-stack electric vehicle management platform", "private": true, "workspaces": ["apps/web", "apps/mobile", "packages/shared"], "scripts": {"dev": "concurrently \"npm run dev:web\" \"npm run dev:mobile\"", "dev:web": "cd apps/web && npm run dev", "dev:mobile": "cd apps/mobile && npm run start", "build": "npm run build:web && npm run build:mobile", "build:web": "cd apps/web && npm run build", "build:mobile": "cd apps/mobile && npm run build", "lint": "npm run lint:web && npm run lint:mobile", "lint:web": "cd apps/web && npm run lint", "lint:mobile": "cd apps/mobile && npm run lint", "type-check": "npm run type-check:web && npm run type-check:mobile", "type-check:web": "cd apps/web && npm run type-check", "type-check:mobile": "cd apps/mobile && npm run type-check", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules", "install:all": "npm install && npm run install:web && npm run install:mobile", "install:web": "cd apps/web && npm install", "install:mobile": "cd apps/mobile && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "GreenMilesEV Team", "license": "MIT"}