import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'Invalid EV model ID format' },
        { status: 400 }
      )
    }

    // Fetch the EV model with all details
    const { data: evModel, error } = await supabase
      .from('ev_models')
      .select(`
        *,
        ev_manufacturers!inner(
          id,
          name,
          logo_url,
          website_url,
          headquarters_country,
          founded_year,
          description
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'EV model not found' },
          { status: 404 }
        )
      }
      console.error('Error fetching EV model:', error)
      return NextResponse.json(
        { error: 'Failed to fetch EV model' },
        { status: 500 }
      )
    }

    // Fetch similar models (same body type, similar price range)
    const priceRange = evModel.price_msrp ? evModel.price_msrp * 0.2 : 1000000 // 20% price range
    const { data: similarModels } = await supabase
      .from('ev_models')
      .select(`
        id,
        make,
        model,
        year,
        trim,
        price_msrp,
        range_epa_miles,
        images,
        is_featured,
        best_value,
        ev_manufacturers!inner(name, logo_url)
      `)
      .eq('body_type', evModel.body_type)
      .neq('id', id)
      .eq('production_status', 'current')
      .gte('price_msrp', evModel.price_msrp ? evModel.price_msrp - priceRange : 0)
      .lte('price_msrp', evModel.price_msrp ? evModel.price_msrp + priceRange : 999999999)
      .order('popularity_score', { ascending: false })
      .limit(6)

    // Calculate some buyer-focused metrics
    const buyerMetrics = {
      costPerMile: evModel.price_msrp && evModel.range_epa_miles 
        ? Math.round((evModel.price_msrp / 100) / evModel.range_epa_miles)
        : null,
      chargingEfficiency: evModel.charging_speed_dc_kw && evModel.battery_capacity_kwh
        ? Math.round((evModel.charging_speed_dc_kw / evModel.battery_capacity_kwh) * 100) / 100
        : null,
      practicalRange: evModel.range_real_world_miles || (evModel.range_epa_miles ? Math.round(evModel.range_epa_miles * 0.85) : null),
      powerToWeightRatio: evModel.motor_power_hp && evModel.curb_weight_lbs
        ? Math.round((evModel.motor_power_hp / evModel.curb_weight_lbs) * 1000) / 1000
        : null
    }

    return NextResponse.json({
      data: {
        ...evModel,
        buyerMetrics,
        similarModels: similarModels || []
      }
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
