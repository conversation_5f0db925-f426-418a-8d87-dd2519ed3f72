import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { EVModel } from '@greenmiles-ev/shared';
import { useComparison } from '../contexts/ComparisonContext';

interface SimilarVehiclesProps {
  vehicles: EVModel[];
  currentVehicleId?: string;
  title?: string;
}

export function SimilarVehicles({ 
  vehicles, 
  currentVehicleId, 
  title = "Similar Vehicles" 
}: SimilarVehiclesProps) {
  const { addToComparison, removeFromComparison, isInComparison } = useComparison();

  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA';
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A';
    return `${range} mi`;
  };

  const getImageSource = (model: EVModel) => {
    if (model.images && model.images.length > 0) {
      return { uri: model.images[0] };
    }
    return null;
  };

  const handleCompareToggle = (model: EVModel) => {
    if (isInComparison(model.id)) {
      removeFromComparison(model.id);
    } else {
      addToComparison(model);
    }
  };

  const handleVehiclePress = (model: EVModel) => {
    router.push(`/ev-models/${model.id}`);
  };

  const getComparisonHighlight = (model: EVModel, currentModel?: EVModel) => {
    if (!currentModel) return null;

    const highlights = [];
    
    // Price comparison
    if (model.price_msrp && currentModel.price_msrp) {
      const priceDiff = model.price_msrp - currentModel.price_msrp;
      if (Math.abs(priceDiff) > 500000) { // $5k difference
        highlights.push({
          type: priceDiff < 0 ? 'better' : 'worse',
          text: `${Math.abs(priceDiff / 100000).toFixed(0)}k ${priceDiff < 0 ? 'less' : 'more'}`,
          icon: 'cash' as keyof typeof Ionicons.glyphMap,
        });
      }
    }

    // Range comparison
    if (model.range_epa_miles && currentModel.range_epa_miles) {
      const rangeDiff = model.range_epa_miles - currentModel.range_epa_miles;
      if (Math.abs(rangeDiff) > 30) {
        highlights.push({
          type: rangeDiff > 0 ? 'better' : 'worse',
          text: `${Math.abs(rangeDiff)} mi ${rangeDiff > 0 ? 'more' : 'less'}`,
          icon: 'speedometer' as keyof typeof Ionicons.glyphMap,
        });
      }
    }

    // Efficiency comparison
    if (model.efficiency_mpge && currentModel.efficiency_mpge) {
      const efficiencyDiff = model.efficiency_mpge - currentModel.efficiency_mpge;
      if (Math.abs(efficiencyDiff) > 10) {
        highlights.push({
          type: efficiencyDiff > 0 ? 'better' : 'worse',
          text: `${Math.abs(efficiencyDiff)} MPGe ${efficiencyDiff > 0 ? 'more' : 'less'}`,
          icon: 'leaf' as keyof typeof Ionicons.glyphMap,
        });
      }
    }

    return highlights.slice(0, 2); // Show max 2 highlights
  };

  const SimilarVehicleCard = ({ model }: { model: EVModel }) => {
    const currentModel = vehicles.find(v => v.id === currentVehicleId);
    const highlights = getComparisonHighlight(model, currentModel);

    return (
      <TouchableOpacity
        onPress={() => handleVehiclePress(model)}
        className="bg-white rounded-xl shadow-sm border border-gray-100 mr-3"
        style={{ width: 280 }}
      >
        {/* Image */}
        <View className="relative">
          {getImageSource(model) ? (
            <Image
              source={getImageSource(model)}
              className="w-full h-40 rounded-t-xl"
              resizeMode="cover"
            />
          ) : (
            <View className="w-full h-40 rounded-t-xl bg-gray-200 items-center justify-center">
              <Ionicons name="car" size={32} color="#9ca3af" />
            </View>
          )}

          {/* Badges */}
          <View className="absolute top-2 left-2 flex-row gap-1">
            {model.is_featured && (
              <View className="bg-electric-600 rounded-full px-2 py-1">
                <Text className="text-white text-xs font-medium">Featured</Text>
              </View>
            )}
            {model.best_value && (
              <View className="bg-green-600 rounded-full px-2 py-1">
                <Text className="text-white text-xs font-medium">Best Value</Text>
              </View>
            )}
          </View>

          {/* Compare Button */}
          <TouchableOpacity
            onPress={() => handleCompareToggle(model)}
            className={`absolute top-2 right-2 p-2 rounded-lg ${
              isInComparison(model.id) ? 'bg-electric-600' : 'bg-white/90'
            }`}
          >
            <Ionicons
              name="analytics"
              size={16}
              color={isInComparison(model.id) ? 'white' : '#6b7280'}
            />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View className="p-4">
          <Text className="text-lg font-semibold text-gray-900" numberOfLines={1}>
            {model.make} {model.model}
          </Text>
          {model.trim && (
            <Text className="text-sm text-gray-600" numberOfLines={1}>
              {model.trim}
            </Text>
          )}
          
          <Text className="text-xl font-bold text-electric-600 mt-2">
            {formatPrice(model.price_msrp)}
          </Text>

          {/* Key Specs */}
          <View className="mt-3 space-y-2">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="speedometer" size={14} color="#6b7280" />
                <Text className="text-xs text-gray-600 ml-1">Range</Text>
              </View>
              <Text className="text-sm font-medium text-gray-900">
                {formatRange(model.range_epa_miles)}
              </Text>
            </View>

            {model.efficiency_mpge && (
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Ionicons name="leaf" size={14} color="#6b7280" />
                  <Text className="text-xs text-gray-600 ml-1">Efficiency</Text>
                </View>
                <Text className="text-sm font-medium text-gray-900">
                  {model.efficiency_mpge} MPGe
                </Text>
              </View>
            )}

            {model.acceleration_0_60_mph && (
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Ionicons name="flash" size={14} color="#6b7280" />
                  <Text className="text-xs text-gray-600 ml-1">0-60 mph</Text>
                </View>
                <Text className="text-sm font-medium text-gray-900">
                  {model.acceleration_0_60_mph}s
                </Text>
              </View>
            )}
          </View>

          {/* Comparison Highlights */}
          {highlights && highlights.length > 0 && (
            <View className="mt-3 pt-3 border-t border-gray-100">
              <Text className="text-xs text-gray-500 mb-2">vs. current vehicle:</Text>
              {highlights.map((highlight, index) => (
                <View key={index} className="flex-row items-center mb-1">
                  <Ionicons
                    name={highlight.icon}
                    size={12}
                    color={highlight.type === 'better' ? '#22c55e' : '#f59e0b'}
                  />
                  <Text
                    className={`text-xs ml-1 ${
                      highlight.type === 'better' ? 'text-green-600' : 'text-orange-600'
                    }`}
                  >
                    {highlight.text}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (vehicles.length === 0) {
    return null;
  }

  return (
    <View className="mb-4">
      <View className="flex-row items-center justify-between px-4 mb-3">
        <Text className="text-lg font-semibold text-gray-900">{title}</Text>
        <TouchableOpacity onPress={() => router.push('/(tabs)/browse')}>
          <Text className="text-electric-600 font-medium">View All</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        {vehicles.map((vehicle) => (
          <SimilarVehicleCard key={vehicle.id} model={vehicle} />
        ))}
      </ScrollView>
    </View>
  );
}
