'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  Users, 
  MapPin, 
  Zap, 
  DollarSign,
  Star,
  TrendingUp,
  Shield,
  Leaf
} from 'lucide-react'
import { formatPrice, formatRange } from '@/shared/utils/ev-buyer-guide'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelOverviewProps {
  evModel: EVModelWithDetails
}

export function EVModelOverview({ evModel }: EVModelOverviewProps) {
  // Generate pros and cons based on the model data
  const generateProsAndCons = () => {
    const pros = []
    const cons = []

    // Range analysis
    if (evModel.range_epa_miles && evModel.range_epa_miles >= 300) {
      pros.push('Excellent driving range')
    } else if (evModel.range_epa_miles && evModel.range_epa_miles < 200) {
      cons.push('Limited driving range')
    }

    // Charging speed analysis
    if (evModel.charging_speed_dc_kw && evModel.charging_speed_dc_kw >= 150) {
      pros.push('Fast DC charging capability')
    } else if (evModel.charging_speed_dc_kw && evModel.charging_speed_dc_kw < 50) {
      cons.push('Slower charging speeds')
    }

    // Price analysis
    if (evModel.price_msrp && evModel.price_msrp < 4000000) { // Under $40k
      pros.push('Competitive pricing')
    } else if (evModel.price_msrp && evModel.price_msrp > 8000000) { // Over $80k
      cons.push('Premium pricing')
    }

    // Acceleration analysis
    if (evModel.acceleration_0_60_mph && evModel.acceleration_0_60_mph <= 4.0) {
      pros.push('Impressive acceleration')
    } else if (evModel.acceleration_0_60_mph && evModel.acceleration_0_60_mph > 8.0) {
      cons.push('Modest acceleration')
    }

    // Efficiency analysis
    if (evModel.efficiency_mpge && evModel.efficiency_mpge >= 120) {
      pros.push('High energy efficiency')
    }

    // Brand reputation
    if (evModel.make === 'Tesla') {
      pros.push('Advanced technology and software')
      pros.push('Extensive Supercharger network')
    }

    // Default pros/cons if we don't have enough data
    if (pros.length === 0) {
      pros.push('Modern electric drivetrain', 'Zero local emissions', 'Lower maintenance costs')
    }
    if (cons.length === 0) {
      cons.push('Charging infrastructure dependency', 'Higher upfront cost')
    }

    return { pros, cons }
  }

  const { pros, cons } = generateProsAndCons()

  // Generate ideal buyer profile
  const generateBuyerProfile = () => {
    const profiles = []

    if (evModel.body_type === 'sedan') {
      profiles.push('Daily commuters seeking efficiency')
    } else if (evModel.body_type === 'suv') {
      profiles.push('Families needing space and versatility')
    } else if (evModel.body_type === 'hatchback') {
      profiles.push('Urban drivers prioritizing maneuverability')
    }

    if (evModel.range_epa_miles && evModel.range_epa_miles >= 300) {
      profiles.push('Long-distance travelers')
    }

    if (evModel.price_msrp && evModel.price_msrp < 4000000) {
      profiles.push('Budget-conscious buyers')
    } else if (evModel.price_msrp && evModel.price_msrp > 8000000) {
      profiles.push('Luxury car enthusiasts')
    }

    if (evModel.acceleration_0_60_mph && evModel.acceleration_0_60_mph <= 5.0) {
      profiles.push('Performance-oriented drivers')
    }

    return profiles.length > 0 ? profiles : ['Electric vehicle enthusiasts', 'Environmentally conscious drivers']
  }

  const buyerProfiles = generateBuyerProfile()

  // Calculate overall score based on various factors
  const calculateOverallScore = () => {
    let score = 70 // Base score
    
    // Range bonus
    if (evModel.range_epa_miles) {
      if (evModel.range_epa_miles >= 400) score += 15
      else if (evModel.range_epa_miles >= 300) score += 10
      else if (evModel.range_epa_miles >= 200) score += 5
      else score -= 5
    }

    // Charging speed bonus
    if (evModel.charging_speed_dc_kw) {
      if (evModel.charging_speed_dc_kw >= 200) score += 10
      else if (evModel.charging_speed_dc_kw >= 100) score += 5
      else if (evModel.charging_speed_dc_kw < 50) score -= 5
    }

    // Efficiency bonus
    if (evModel.efficiency_mpge) {
      if (evModel.efficiency_mpge >= 130) score += 10
      else if (evModel.efficiency_mpge >= 110) score += 5
    }

    // Featured/awards bonus
    if (evModel.is_featured) score += 5
    if (evModel.best_value) score += 5
    if (evModel.editor_choice) score += 10

    return Math.min(Math.max(score, 0), 100)
  }

  const overallScore = calculateOverallScore()

  return (
    <div className="space-y-8">
      {/* Key Highlights */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <div className="rounded-full bg-electric-100 p-3 dark:bg-electric-900">
              <Zap className="h-6 w-6 text-electric-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {formatRange(evModel.range_epa_miles)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                EPA Range
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {evModel.buyerMetrics.costPerMile ? `$${evModel.buyerMetrics.costPerMile}` : 'N/A'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Cost per Mile
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {evModel.efficiency_mpge || 'N/A'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                MPGe Efficiency
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <div className="rounded-full bg-amber-100 p-3 dark:bg-amber-900">
              <Star className="h-6 w-6 text-amber-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {overallScore}/100
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Overall Score
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Pros and Cons */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Pros & Cons
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Pros */}
            <div>
              <h4 className="mb-3 font-semibold text-green-700 dark:text-green-400">
                Strengths
              </h4>
              <div className="space-y-2">
                {pros.map((pro, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <CheckCircle className="mt-0.5 h-4 w-4 text-green-600" />
                    <span className="text-sm">{pro}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Cons */}
            <div>
              <h4 className="mb-3 font-semibold text-red-700 dark:text-red-400">
                Considerations
              </h4>
              <div className="space-y-2">
                {cons.map((con, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <XCircle className="mt-0.5 h-4 w-4 text-red-600" />
                    <span className="text-sm">{con}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ideal Buyer Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Ideal For
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {buyerProfiles.map((profile, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="h-2 w-2 rounded-full bg-electric-600" />
                  <span className="text-sm">{profile}</span>
                </div>
              ))}
            </div>

            {/* Market Regions */}
            {evModel.market_regions && evModel.market_regions.length > 0 && (
              <div className="mt-6">
                <h4 className="mb-3 flex items-center gap-2 font-semibold">
                  <MapPin className="h-4 w-4" />
                  Available Markets
                </h4>
                <div className="flex flex-wrap gap-2">
                  {evModel.market_regions.map((region, index) => (
                    <Badge key={index} variant="outline">
                      {region}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Overall Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Leaf className="h-5 w-5" />
            Overall Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Overall Score</span>
              <span className="text-2xl font-bold text-electric-600">
                {overallScore}/100
              </span>
            </div>
            <Progress value={overallScore} className="h-3" />
            
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {overallScore >= 85 && "Excellent choice with outstanding performance and features."}
              {overallScore >= 70 && overallScore < 85 && "Solid option with good balance of features and value."}
              {overallScore >= 55 && overallScore < 70 && "Decent choice with some trade-offs to consider."}
              {overallScore < 55 && "May have limitations that could affect your satisfaction."}
            </p>

            {/* Key Recommendation */}
            <div className="rounded-lg bg-electric-50 p-4 dark:bg-electric-950">
              <h4 className="mb-2 font-semibold text-electric-900 dark:text-electric-100">
                Bottom Line
              </h4>
              <p className="text-sm text-electric-800 dark:text-electric-200">
                The {evModel.make} {evModel.model} offers {formatRange(evModel.range_epa_miles)} of range 
                at {formatPrice(evModel.price_msrp)}, making it {evModel.best_value ? 'an excellent value proposition' : 
                evModel.price_msrp && evModel.price_msrp > 8000000 ? 'a premium choice' : 'a competitive option'} 
                in the {evModel.body_type} segment.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
