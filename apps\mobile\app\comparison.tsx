import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Image, Dimensions, Alert } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import { useComparison } from '../src/contexts/ComparisonContext'
import { ComparisonOverview } from '../src/components/ComparisonOverview'
import { ComparisonSpecs } from '../src/components/ComparisonSpecs'
import { ComparisonCosts } from '../src/components/ComparisonCosts'
import { DecisionMatrix } from '../src/components/DecisionMatrix'
import { ComparisonRecommendations } from '../src/components/ComparisonRecommendations'

const { width: screenWidth } = Dimensions.get('window')

type TabType = 'overview' | 'specs' | 'costs' | 'matrix' | 'recommendations'

interface Tab {
  id: TabType
  label: string
  icon: keyof typeof Ionicons.glyphMap
}

const tabs: Tab[] = [
  { id: 'overview', label: 'Overview', icon: 'eye' },
  { id: 'specs', label: 'Specs', icon: 'list' },
  { id: 'costs', label: 'Costs', icon: 'calculator' },
  { id: 'matrix', label: 'Decision', icon: 'analytics' },
  { id: 'recommendations', label: 'Insights', icon: 'bulb' },
]

export default function ComparisonScreen() {
  const { comparisonList, removeFromComparison, clearComparison } = useComparison()
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [showVehicleManager, setShowVehicleManager] = useState(false)

  const handleShare = () => {
    // TODO: Implement sharing functionality
    Alert.alert('Share Comparison', 'Sharing functionality coming soon!')
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    Alert.alert('Export Comparison', 'Export functionality coming soon!')
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <ComparisonOverview models={comparisonList} />
      case 'specs':
        return <ComparisonSpecs models={comparisonList} />
      case 'costs':
        return <ComparisonCosts models={comparisonList} />
      case 'matrix':
        return <DecisionMatrix models={comparisonList} />
      case 'recommendations':
        return <ComparisonRecommendations models={comparisonList} />
      default:
        return <ComparisonOverview models={comparisonList} />
    }
  }

  if (comparisonList.length === 0) {
    return (
      <View className="flex-1 bg-white">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 pt-12 bg-electric-600">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text className="text-lg font-semibold text-white">Compare Vehicles</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Empty State */}
        <View className="flex-1 justify-center items-center px-8">
          <View className="bg-gray-100 rounded-full p-6 mb-6">
            <Ionicons name="analytics-outline" size={48} color="#6b7280" />
          </View>
          <Text className="text-2xl font-bold text-gray-900 mb-3 text-center">
            Start Your Comparison
          </Text>
          <Text className="text-gray-600 text-center mb-8 leading-6">
            Add vehicles from the browse screen to compare their features, specifications, and costs
            side-by-side.
          </Text>

          <View className="w-full space-y-3">
            <TouchableOpacity
              onPress={() => router.push('/(tabs)/browse')}
              className="bg-electric-600 rounded-xl py-4 items-center"
            >
              <Text className="text-white font-semibold text-lg">Browse Vehicles</Text>
            </TouchableOpacity>

            <View className="bg-blue-50 rounded-xl p-4">
              <View className="flex-row items-center mb-2">
                <Ionicons name="information-circle" size={20} color="#3b82f6" />
                <Text className="font-medium text-blue-900 ml-2">Pro Tip</Text>
              </View>
              <Text className="text-blue-800 text-sm">
                You can compare up to 4 vehicles at once. Use our decision matrix to find the
                perfect EV for your needs.
              </Text>
            </View>
          </View>
        </View>
      </View>
    )
  }

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 pt-12 bg-electric-600">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-white">
          Compare {comparisonList.length} Vehicle{comparisonList.length > 1 ? 's' : ''}
        </Text>
        <View className="flex-row items-center gap-3">
          <TouchableOpacity onPress={handleShare}>
            <Ionicons name="share-outline" size={24} color="white" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleExport}>
            <Ionicons name="download-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View className="bg-white border-b border-gray-200">
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-4">
          <View className="flex-row gap-1">
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                onPress={() => setActiveTab(tab.id)}
                className={`px-4 py-3 rounded-lg mr-2 ${
                  activeTab === tab.id ? 'bg-electric-600' : 'bg-gray-100'
                }`}
              >
                <View className="flex-row items-center">
                  <Ionicons
                    name={tab.icon}
                    size={16}
                    color={activeTab === tab.id ? 'white' : '#6b7280'}
                  />
                  <Text
                    className={`ml-2 font-medium ${
                      activeTab === tab.id ? 'text-white' : 'text-gray-700'
                    }`}
                  >
                    {tab.label}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Tab Content */}
      <ScrollView className="flex-1">{renderTabContent()}</ScrollView>

      {/* Floating Action Bar */}
      <View className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <View className="flex-row gap-3">
          <TouchableOpacity
            onPress={() => router.push('/(tabs)/browse')}
            className="flex-1 bg-electric-600 rounded-xl py-3 items-center"
          >
            <Text className="text-white font-semibold">Add More</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={clearComparison}
            className="px-6 py-3 bg-gray-100 rounded-xl items-center justify-center"
          >
            <Ionicons name="trash-outline" size={20} color="#374151" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}
