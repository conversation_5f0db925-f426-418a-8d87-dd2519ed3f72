'use client'

import { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON> } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { EVModelCard } from '@/components/ev-models/EVModelCard'
import { EVModelFilters } from '@/components/ev-models/EVModelFilters'
import { EVModelSearch } from '@/components/ev-models/EVModelSearch'
import { IntelligentSearch } from '@/components/ev-models/IntelligentSearch'
import { SmartDecisionFilters } from '@/components/ev-models/SmartDecisionFilters'
import { EVModelSort } from '@/components/ev-models/EVModelSort'
import { EVModelPagination } from '@/components/ev-models/EVModelPagination'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Grid, List, Filter, X } from 'lucide-react'
import { useEVModels } from '@/hooks/useEVModels'
import { useEVFilters } from '@/hooks/useEVFilters'
import { cn } from '@/lib/utils'
import type { EVModel, EVModelFilters as FilterType, SortOptions } from '@/shared/types'

export default function EVModelsPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [searchMode, setSearchMode] = useState<'basic' | 'intelligent'>('intelligent')
  const [showSmartFilters, setShowSmartFilters] = useState(false)

  const {
    filters,
    sortOptions,
    searchQuery,
    updateFilters,
    updateSort,
    updateSearch,
    clearFilters,
    hasActiveFilters,
  } = useEVFilters()

  const { evModels, loading, error, pagination, refetch } = useEVModels({
    filters,
    sortOptions,
    searchQuery,
  })

  // Featured models for hero section
  const featuredModels = useMemo(() => {
    return evModels?.filter((model) => model.is_featured).slice(0, 3) || []
  }, [evModels])

  const handleFilterChange = (newFilters: Partial<FilterType>) => {
    updateFilters(newFilters)
  }

  const handleSortChange = (newSort: SortOptions) => {
    updateSort(newSort)
  }

  const handleSearchChange = (query: string) => {
    updateSearch(query)
  }

  const handleClearFilters = () => {
    clearFilters()
    setShowFilters(false)
  }

  const handleSmartFiltersChange = (newFilters: Partial<FilterType>) => {
    updateFilters(newFilters)
    setShowSmartFilters(false)
  }

  const handleQuickFilter = (filterData: any) => {
    // Handle quick filter application from intelligent search
    console.log('Quick filter applied:', filterData)
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header variant="dashboard" />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-electric-600 to-electric-700 py-12 text-white">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="mb-4 text-4xl font-bold md:text-5xl">
                Discover Your Perfect Electric Vehicle
              </h1>
              <p className="mb-8 text-xl text-electric-100 md:text-2xl">
                Compare specifications, features, and find the EV that matches your lifestyle
              </p>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <Card className="border-electric-500 bg-electric-600/20 backdrop-blur-sm">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-white">{pagination?.total || 0}</div>
                    <div className="text-electric-100">EV Models</div>
                  </CardContent>
                </Card>
                <Card className="border-electric-500 bg-electric-600/20 backdrop-blur-sm">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-white">{featuredModels.length}</div>
                    <div className="text-electric-100">Featured Models</div>
                  </CardContent>
                </Card>
                <Card className="border-electric-500 bg-electric-600/20 backdrop-blur-sm">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-white">15+</div>
                    <div className="text-electric-100">Manufacturers</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Search and Filters Section */}
        <section className="border-b bg-white py-6 dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              {/* Enhanced Search */}
              <div className="flex-1 lg:max-w-2xl">
                <div className="space-y-3">
                  {/* Search Mode Toggle */}
                  <div className="flex gap-2">
                    <Button
                      variant={searchMode === 'intelligent' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSearchMode('intelligent')}
                    >
                      🧠 Smart Search
                    </Button>
                    <Button
                      variant={searchMode === 'basic' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSearchMode('basic')}
                    >
                      🔍 Basic Search
                    </Button>
                    <Button
                      variant={showSmartFilters ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setShowSmartFilters(!showSmartFilters)}
                    >
                      🎯 Decision Helper
                    </Button>
                  </div>

                  {/* Search Input */}
                  {searchMode === 'intelligent' ? (
                    <IntelligentSearch
                      value={searchQuery}
                      onChange={handleSearchChange}
                      onQuickFilter={handleQuickFilter}
                      placeholder="Describe what you're looking for: 'affordable family SUV with long range'..."
                    />
                  ) : (
                    <EVModelSearch
                      value={searchQuery}
                      onChange={handleSearchChange}
                      placeholder="Search by make, model, or features..."
                    />
                  )}
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center gap-4">
                {/* Sort */}
                <EVModelSort value={sortOptions} onChange={handleSortChange} />

                {/* View Mode Toggle */}
                <div className="flex rounded-lg border">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>

                {/* Filter Toggle */}
                <Button
                  variant={showFilters ? 'default' : 'outline'}
                  onClick={() => setShowFilters(!showFilters)}
                  className="relative"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                  {hasActiveFilters && (
                    <Badge className="ml-2 h-5 w-5 rounded-full p-0 text-xs">!</Badge>
                  )}
                </Button>
              </div>
            </div>

            {/* Active Filters */}
            {hasActiveFilters && (
              <div className="mt-4 flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>
                {/* Filter badges will be rendered here */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFilters}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="mr-1 h-3 w-3" />
                  Clear all
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Smart Decision Filters */}
        {showSmartFilters && (
          <section className="border-b bg-gray-50 py-6 dark:bg-gray-800">
            <div className="container mx-auto px-4">
              <SmartDecisionFilters
                onFiltersChange={handleSmartFiltersChange}
                onClose={() => setShowSmartFilters(false)}
              />
            </div>
          </section>
        )}

        {/* Main Content */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <div className="flex gap-8">
              {/* Filters Sidebar */}
              {showFilters && (
                <aside className="w-80 shrink-0">
                  <div className="sticky top-4">
                    <EVModelFilters
                      filters={filters}
                      onChange={handleFilterChange}
                      onClose={() => setShowFilters(false)}
                    />
                  </div>
                </aside>
              )}

              {/* Results */}
              <div className="flex-1">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : error ? (
                  <Alert variant="destructive">
                    <AlertDescription>Failed to load EV models. Please try again.</AlertDescription>
                  </Alert>
                ) : evModels && evModels.length > 0 ? (
                  <>
                    {/* Results Header */}
                    <div className="mb-6 flex items-center justify-between">
                      <h2 className="text-lg font-semibold">{pagination?.total} EV Models Found</h2>
                    </div>

                    {/* Results Grid/List */}
                    <div
                      className={cn(
                        'gap-6',
                        viewMode === 'grid'
                          ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3'
                          : 'space-y-4'
                      )}
                    >
                      {evModels.map((model) => (
                        <EVModelCard key={model.id} model={model} viewMode={viewMode} />
                      ))}
                    </div>

                    {/* Pagination */}
                    {pagination && pagination.totalPages > 1 && (
                      <div className="mt-8">
                        <EVModelPagination
                          pagination={pagination}
                          onPageChange={(page) => {
                            // Handle page change
                            window.scrollTo({ top: 0, behavior: 'smooth' })
                          }}
                        />
                      </div>
                    )}
                  </>
                ) : (
                  <div className="py-12 text-center">
                    <h3 className="mb-2 text-lg font-semibold">No EV models found</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Try adjusting your search criteria or filters.
                    </p>
                    {hasActiveFilters && (
                      <Button variant="outline" onClick={handleClearFilters} className="mt-4">
                        Clear filters
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
