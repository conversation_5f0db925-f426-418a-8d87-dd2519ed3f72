import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SmartDecisionFilters } from '@/components/ev-models/SmartDecisionFilters'

describe('SmartDecisionFilters', () => {
  const mockOnFiltersChange = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  const renderSmartDecisionFilters = (props = {}) => {
    return render(
      <SmartDecisionFilters
        onFiltersChange={mockOnFiltersChange}
        onClose={mockOnClose}
        {...props}
      />
    )
  }

  describe('Tab Navigation', () => {
    it('should render all tabs', () => {
      renderSmartDecisionFilters()
      
      expect(screen.getByText('Buyer Profiles')).toBeInTheDocument()
      expect(screen.getByText('Lifestyle Quiz')).toBeInTheDocument()
      expect(screen.getByText('Smart Match')).toBeInTheDocument()
    })

    it('should switch between tabs', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      // Default should be profiles tab
      expect(screen.getByText('Choose a profile that matches your EV buying needs')).toBeInTheDocument()
      
      // Switch to lifestyle tab
      await user.click(screen.getByText('Lifestyle Quiz'))
      expect(screen.getByText('Tell us about your lifestyle to get personalized recommendations')).toBeInTheDocument()
      
      // Switch to smart match tab
      await user.click(screen.getByText('Smart Match'))
      expect(screen.getByText('AI-powered recommendations based on your preferences')).toBeInTheDocument()
    })
  })

  describe('Buyer Profiles', () => {
    it('should display all buyer profiles', () => {
      renderSmartDecisionFilters()
      
      expect(screen.getByText('First-Time EV Buyer')).toBeInTheDocument()
      expect(screen.getByText('Daily Commuter')).toBeInTheDocument()
      expect(screen.getByText('Family Vehicle')).toBeInTheDocument()
      expect(screen.getByText('Luxury & Performance')).toBeInTheDocument()
      expect(screen.getByText('Budget-Friendly')).toBeInTheDocument()
      expect(screen.getByText('Long-Distance Traveler')).toBeInTheDocument()
    })

    it('should apply filters when profile is selected', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      await user.click(screen.getByText('First-Time EV Buyer'))
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        priceMax: '6000000',
        rangeMin: '250',
        featured: 'true'
      })
    })

    it('should apply different filters for different profiles', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      await user.click(screen.getByText('Budget-Friendly'))
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        priceMax: '4000000',
        bestValue: 'true'
      })
    })
  })

  describe('Lifestyle Quiz', () => {
    beforeEach(async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      await user.click(screen.getByText('Lifestyle Quiz'))
    })

    it('should display all lifestyle inputs', () => {
      expect(screen.getByText('Budget Range')).toBeInTheDocument()
      expect(screen.getByText('Daily Driving Miles')).toBeInTheDocument()
      expect(screen.getByText('Primary Use')).toBeInTheDocument()
      expect(screen.getByText('Typical Passengers')).toBeInTheDocument()
      expect(screen.getByText('Home Charging Available')).toBeInTheDocument()
    })

    it('should update budget range slider', async () => {
      const user = userEvent.setup()
      
      const budgetSlider = screen.getAllByRole('slider')[0] // First slider is budget
      await user.click(budgetSlider)
      
      // Slider should be interactive
      expect(budgetSlider).toBeInTheDocument()
    })

    it('should update daily miles slider', async () => {
      const user = userEvent.setup()
      
      const milesSlider = screen.getAllByRole('slider')[1] // Second slider is daily miles
      await user.click(milesSlider)
      
      expect(milesSlider).toBeInTheDocument()
    })

    it('should change primary use selection', async () => {
      const user = userEvent.setup()
      
      const primaryUseSelect = screen.getByRole('combobox')
      await user.click(primaryUseSelect)
      
      await waitFor(() => {
        expect(screen.getByText('Family Transportation')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Family Transportation'))
      
      // Selection should be updated
      expect(primaryUseSelect).toHaveValue('family')
    })

    it('should toggle home charging availability', async () => {
      const user = userEvent.setup()
      
      const noButton = screen.getByRole('button', { name: 'No' })
      await user.click(noButton)
      
      // Button should be selected
      expect(noButton).toHaveClass('default') // Assuming selected state has 'default' variant
    })

    it('should generate recommendations based on inputs', async () => {
      const user = userEvent.setup()
      
      const recommendButton = screen.getByText('Get My Recommendations')
      await user.click(recommendButton)
      
      expect(mockOnFiltersChange).toHaveBeenCalled()
      
      // Should apply filters based on default lifestyle inputs
      const appliedFilters = mockOnFiltersChange.mock.calls[0][0]
      expect(appliedFilters).toHaveProperty('priceMin')
      expect(appliedFilters).toHaveProperty('priceMax')
    })

    it('should adjust recommendations based on family size', async () => {
      const user = userEvent.setup()
      
      // Increase family size to trigger SUV recommendation
      const familySizeSlider = screen.getAllByRole('slider')[2] // Third slider is family size
      fireEvent.change(familySizeSlider, { target: { value: '6' } })
      
      const recommendButton = screen.getByText('Get My Recommendations')
      await user.click(recommendButton)
      
      const appliedFilters = mockOnFiltersChange.mock.calls[0][0]
      expect(appliedFilters.bodyType).toBe('suv')
    })

    it('should adjust range requirements based on daily miles and charging', async () => {
      const user = userEvent.setup()
      
      // Set high daily miles and no home charging
      const milesSlider = screen.getAllByRole('slider')[1]
      fireEvent.change(milesSlider, { target: { value: '150' } })
      
      const noChargingButton = screen.getByRole('button', { name: 'No' })
      await user.click(noChargingButton)
      
      const recommendButton = screen.getByText('Get My Recommendations')
      await user.click(recommendButton)
      
      const appliedFilters = mockOnFiltersChange.mock.calls[0][0]
      expect(parseInt(appliedFilters.rangeMin)).toBeGreaterThanOrEqual(300)
    })
  })

  describe('Smart Match Tab', () => {
    it('should show coming soon message', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      await user.click(screen.getByText('Smart Match'))
      
      expect(screen.getByText('Smart Recommendations Coming Soon')).toBeInTheDocument()
      expect(screen.getByText(/AI will analyze your preferences/)).toBeInTheDocument()
    })
  })

  describe('Filter Management', () => {
    it('should clear all filters when clear button is clicked', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      const clearButton = screen.getByText('Clear All')
      await user.click(clearButton)
      
      expect(mockOnFiltersChange).toHaveBeenCalledWith({
        make: undefined,
        bodyType: undefined,
        priceMin: undefined,
        priceMax: undefined,
        rangeMin: undefined,
        productionStatus: undefined,
        featured: undefined,
        bestValue: undefined
      })
    })

    it('should close when close button is clicked', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      const closeButton = screen.getByText('Close')
      await user.click(closeButton)
      
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  describe('Profile Descriptions', () => {
    it('should show appropriate descriptions for each profile', () => {
      renderSmartDecisionFilters()
      
      expect(screen.getByText('New to EVs, looking for reliable and affordable options')).toBeInTheDocument()
      expect(screen.getByText('Efficient, reliable EVs for daily work commutes')).toBeInTheDocument()
      expect(screen.getByText('Spacious SUVs and crossovers for families')).toBeInTheDocument()
      expect(screen.getByText('Premium EVs with advanced features and performance')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      renderSmartDecisionFilters()
      
      expect(screen.getByRole('heading', { name: /smart decision filters/i })).toBeInTheDocument()
    })

    it('should have accessible form controls', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      await user.click(screen.getByText('Lifestyle Quiz'))
      
      // All sliders should have labels
      const sliders = screen.getAllByRole('slider')
      sliders.forEach(slider => {
        expect(slider).toHaveAccessibleName()
      })
    })

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup()
      renderSmartDecisionFilters()
      
      // Tab navigation should work
      await user.tab()
      expect(document.activeElement).toHaveAttribute('role', 'button')
    })
  })

  describe('Responsive Design', () => {
    it('should render properly on different screen sizes', () => {
      renderSmartDecisionFilters()
      
      // Grid layout should be responsive
      const profileGrid = screen.getByText('First-Time EV Buyer').closest('.grid')
      expect(profileGrid).toHaveClass('md:grid-cols-2')
    })
  })
})
