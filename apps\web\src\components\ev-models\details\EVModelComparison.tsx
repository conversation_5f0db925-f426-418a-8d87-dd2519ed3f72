'use client'

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelComparisonProps {
  evModel: EVModelWithDetails
}

export function EVModelComparison({ evModel }: EVModelComparisonProps) {
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Similar Models</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-400">
            Model comparison and alternatives coming soon...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
