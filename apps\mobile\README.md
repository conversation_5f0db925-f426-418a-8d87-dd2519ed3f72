# GreenMilesEV Mobile App

A React Native mobile application for electric vehicle management built with Expo, TypeScript, and NativeWind.

## 🚀 Features

- **Cross-Platform**: iOS and Android support with Expo
- **Modern Navigation**: Expo Router with tab-based navigation
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Authentication**: Supabase Auth integration
- **Real-time Data**: Supabase real-time subscriptions
- **Native Feel**: Platform-specific UI components

## 📁 Project Structure

```
app/
├── (tabs)/             # Tab navigation screens
│   ├── _layout.tsx    # Tab layout configuration
│   ├── index.tsx      # Dashboard screen
│   ├── vehicles.tsx   # Vehicle management
│   ├── charging.tsx   # Charging stations
│   ├── trips.tsx      # Trip history
│   └── profile.tsx    # User profile
├── _layout.tsx        # Root layout
└── modal.tsx          # Modal screens
src/
├── components/        # Reusable components
├── hooks/            # Custom React hooks
├── lib/              # Utilities and configurations
├── types/            # TypeScript type definitions
└── utils/            # Helper functions
```

## 🛠️ Development

### Prerequisites

- Node.js 18+ and npm 9+
- Expo CLI: `npm install -g @expo/cli`
- iOS Simulator (macOS) or Android Studio
- Expo Go app on your device (for testing)

### Setup

1. Navigate to the mobile app directory:
```bash
cd apps/mobile
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Choose your platform:
   - Press `i` for iOS Simulator
   - Press `a` for Android Emulator
   - Scan QR code with Expo Go app

### Available Scripts

- `npm start` - Start Expo development server
- `npm run android` - Start on Android
- `npm run ios` - Start on iOS
- `npm run web` - Start web version
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 📱 Screens

### Dashboard
- Battery status and range
- Quick actions (find charging, vehicle controls)
- Recent activity feed

### Vehicles
- Vehicle information and stats
- Remote controls (climate, lock/unlock)
- Maintenance tracking

### Charging
- Current charging status
- Nearby charging stations with availability
- Charging history and costs

### Trips
- Trip history and analytics
- Efficiency tracking
- Carbon footprint and savings

### Profile
- User information and stats
- App settings and preferences
- Support and help

## 🎨 Design System

### Colors
- **Electric Green**: Primary brand color (#22c55e)
- **Grays**: Text and background colors
- **Status Colors**: Success, warning, error indicators

### Typography
- **Inter Font**: Clean, modern typeface
- **Consistent Sizing**: Tailwind's type scale

### Components
- **Native Icons**: Ionicons for consistent iconography
- **Cards**: Rounded containers with shadows
- **Buttons**: Various styles and states

## 🔧 Configuration

### NativeWind
Tailwind CSS classes work directly in React Native:
```jsx
<View className="bg-white p-4 rounded-xl shadow-sm">
  <Text className="text-lg font-bold text-gray-900">Title</Text>
</View>
```

### Expo Router
File-based routing with TypeScript support:
- `app/(tabs)/` - Tab navigation
- `app/modal.tsx` - Modal screens
- `app/_layout.tsx` - Root layout

### TypeScript
Configured with:
- Strict mode enabled
- Path aliases for clean imports
- Shared types from packages/shared

## 🚀 Building & Deployment

### Development Build
```bash
npm run build
```

### Production Build
```bash
# Android
npm run build:android

# iOS
npm run build:ios
```

### App Store Deployment
1. Configure app.json with store information
2. Build production version
3. Submit to App Store/Google Play

## 🔗 Integration

### Supabase
The app integrates with Supabase for:
- User authentication
- Real-time data synchronization
- Offline support with local caching

### Shared Code
Uses shared types and utilities from `packages/shared` for consistency with the web app.

## 📊 Performance

- **Expo Router**: Fast navigation with native performance
- **NativeWind**: Optimized styling with minimal runtime overhead
- **TypeScript**: Compile-time optimizations and error catching

## 🧪 Testing

```bash
npm test
```

Uses Jest and React Native Testing Library for unit and integration tests.
