-- Row Level Security (RLS) Policies for EV Buyer's Guide
-- Migration: 002_create_rls_policies.sql
-- Description: Set up security policies for EV buyer guide tables

-- Enable RLS on all tables
ALTER TABLE ev_manufacturers ENABLE ROW LEVEL SECURITY;
ALTER TABLE ev_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE comparison_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_ev_preferences ENABLE ROW LEVEL SECURITY;

-- EV Manufacturers Policies
-- Public read access for all authenticated users
CREATE POLICY "ev_manufacturers_select_policy" ON ev_manufacturers
  FOR SELECT TO authenticated
  USING (true);

-- Admin-only write access (for future admin panel)
CREATE POLICY "ev_manufacturers_insert_policy" ON ev_manufacturers
  FOR INSERT TO authenticated
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "ev_manufacturers_update_policy" ON ev_manufacturers
  FOR UPDATE TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin')
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "ev_manufacturers_delete_policy" ON ev_manufacturers
  FOR DELETE TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin');

-- EV Models Policies
-- Public read access for all authenticated users
CREATE POLICY "ev_models_select_policy" ON ev_models
  FOR SELECT TO authenticated
  USING (true);

-- Admin-only write access (for future admin panel)
CREATE POLICY "ev_models_insert_policy" ON ev_models
  FOR INSERT TO authenticated
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "ev_models_update_policy" ON ev_models
  FOR UPDATE TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin')
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "ev_models_delete_policy" ON ev_models
  FOR DELETE TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin');

-- User Favorites Policies
-- Users can only access their own favorites
CREATE POLICY "user_favorites_select_policy" ON user_favorites
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_favorites_insert_policy" ON user_favorites
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_favorites_update_policy" ON user_favorites
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_favorites_delete_policy" ON user_favorites
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- Comparison Sessions Policies
-- Users can access their own sessions and public sessions
CREATE POLICY "comparison_sessions_select_policy" ON comparison_sessions
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id OR is_public = true);

-- Users can only create/modify their own sessions
CREATE POLICY "comparison_sessions_insert_policy" ON comparison_sessions
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "comparison_sessions_update_policy" ON comparison_sessions
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "comparison_sessions_delete_policy" ON comparison_sessions
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- User EV Preferences Policies
-- Users can only access their own preferences
CREATE POLICY "user_ev_preferences_select_policy" ON user_ev_preferences
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "user_ev_preferences_insert_policy" ON user_ev_preferences
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_ev_preferences_update_policy" ON user_ev_preferences
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_ev_preferences_delete_policy" ON user_ev_preferences
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- Create helper functions for common queries
CREATE OR REPLACE FUNCTION get_user_favorite_ev_models(user_uuid UUID)
RETURNS TABLE (
  ev_model_id UUID,
  make VARCHAR,
  model VARCHAR,
  year INTEGER,
  price_msrp INTEGER,
  range_epa_miles INTEGER,
  favorited_at TIMESTAMP WITH TIME ZONE
) 
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    em.id,
    em.make,
    em.model,
    em.year,
    em.price_msrp,
    em.range_epa_miles,
    uf.created_at
  FROM user_favorites uf
  JOIN ev_models em ON uf.ev_model_id = em.id
  WHERE uf.user_id = user_uuid
  ORDER BY uf.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get recommended EV models based on user preferences
CREATE OR REPLACE FUNCTION get_recommended_ev_models(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  make VARCHAR,
  model VARCHAR,
  year INTEGER,
  price_msrp INTEGER,
  range_epa_miles INTEGER,
  match_score DECIMAL
) 
SECURITY DEFINER
AS $$
DECLARE
  user_prefs RECORD;
BEGIN
  -- Get user preferences
  SELECT * INTO user_prefs 
  FROM user_ev_preferences 
  WHERE user_id = user_uuid;
  
  -- If no preferences, return popular models
  IF user_prefs IS NULL THEN
    RETURN QUERY
    SELECT 
      em.id,
      em.make,
      em.model,
      em.year,
      em.price_msrp,
      em.range_epa_miles,
      em.popularity_score::DECIMAL / 100 as match_score
    FROM ev_models em
    WHERE em.production_status = 'current'
    ORDER BY em.popularity_score DESC, em.is_featured DESC
    LIMIT limit_count;
    RETURN;
  END IF;
  
  -- Return models matching user preferences with scoring
  RETURN QUERY
  SELECT 
    em.id,
    em.make,
    em.model,
    em.year,
    em.price_msrp,
    em.range_epa_miles,
    (
      CASE 
        WHEN em.price_msrp BETWEEN COALESCE(user_prefs.budget_min, 0) AND COALESCE(user_prefs.budget_max, 999999999) THEN 30
        ELSE 0
      END +
      CASE 
        WHEN em.range_epa_miles >= COALESCE(user_prefs.range_requirement_miles, 0) THEN 25
        ELSE 0
      END +
      CASE 
        WHEN user_prefs.body_type_preferences IS NULL OR em.body_type = ANY(user_prefs.body_type_preferences) THEN 20
        ELSE 0
      END +
      CASE 
        WHEN em.is_featured THEN 10
        ELSE 0
      END +
      CASE 
        WHEN em.best_value THEN 10
        ELSE 0
      END +
      (em.popularity_score::DECIMAL / 10)
    )::DECIMAL as match_score
  FROM ev_models em
  WHERE em.production_status = 'current'
    AND (user_prefs.budget_max IS NULL OR em.price_msrp <= user_prefs.budget_max)
    AND (user_prefs.range_requirement_miles IS NULL OR em.range_epa_miles >= user_prefs.range_requirement_miles)
  ORDER BY match_score DESC, em.popularity_score DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;
