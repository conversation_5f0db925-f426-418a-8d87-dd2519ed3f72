import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function ProfileScreen() {
  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Profile Header */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
        <View className="items-center mb-4">
          <View className="w-20 h-20 bg-electric-100 rounded-full items-center justify-center mb-3">
            <Ionicons name="person" size={32} color="#22c55e" />
          </View>
          <Text className="text-xl font-bold text-gray-900">John <PERSON></Text>
          <Text className="text-gray-600"><EMAIL></Text>
        </View>
        
        <TouchableOpacity className="bg-electric-600 rounded-lg p-3 items-center">
          <Text className="text-white font-semibold">Edit Profile</Text>
        </TouchableOpacity>
      </View>

      {/* Stats */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Your Impact</Text>
        <View className="bg-white rounded-xl p-4 shadow-sm">
          <View className="flex-row justify-between mb-4">
            <View className="items-center">
              <Text className="text-2xl font-bold text-electric-600">2,847</Text>
              <Text className="text-sm text-gray-600">Miles Driven</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-electric-600">342</Text>
              <Text className="text-sm text-gray-600">lbs CO₂ Saved</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-electric-600">$267</Text>
              <Text className="text-sm text-gray-600">Money Saved</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Settings */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Settings</Text>
        
        <View className="bg-white rounded-xl shadow-sm">
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="notifications-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Notifications</Text>
                <Text className="text-sm text-gray-600">Charging alerts and updates</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="shield-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Privacy & Security</Text>
                <Text className="text-sm text-gray-600">Manage your data and privacy</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="card-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Payment Methods</Text>
                <Text className="text-sm text-gray-600">Manage charging payments</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4">
            <View className="flex-row items-center">
              <Ionicons name="settings-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">App Preferences</Text>
                <Text className="text-sm text-gray-600">Units, language, and more</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Support */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Support</Text>
        
        <View className="bg-white rounded-xl shadow-sm">
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="help-circle-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Help Center</Text>
                <Text className="text-sm text-gray-600">FAQs and guides</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="chatbubble-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Contact Support</Text>
                <Text className="text-sm text-gray-600">Get help from our team</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4">
            <View className="flex-row items-center">
              <Ionicons name="information-circle-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">About</Text>
                <Text className="text-sm text-gray-600">App version and info</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sign Out */}
      <View className="mx-4 mt-6 mb-6">
        <TouchableOpacity className="bg-red-600 rounded-xl p-4 items-center">
          <View className="flex-row items-center">
            <Ionicons name="log-out-outline" size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Sign Out</Text>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
