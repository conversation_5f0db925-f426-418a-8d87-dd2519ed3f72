import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { VEHICLE_MAKES, EV_BODY_TYPES } from '@greenmiles-ev/shared';

interface SearchFiltersProps {
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  onClose: () => void;
}

export function SearchFilters({ filters, onFiltersChange, onClose }: SearchFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  const formatPrice = (priceInCents: number) => {
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const updateLocalFilter = (key: string, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const clearAllFilters = () => {
    const clearedFilters = Object.keys(localFilters).reduce((acc, key) => {
      acc[key] = undefined;
      return acc;
    }, {} as Record<string, any>);
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const FilterSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View className="mb-6">
      <Text className="text-lg font-semibold text-gray-900 mb-3">{title}</Text>
      {children}
    </View>
  );

  const FilterOption = ({ 
    label, 
    selected, 
    onPress 
  }: { 
    label: string; 
    selected: boolean; 
    onPress: () => void;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`px-4 py-2 rounded-lg border mr-2 mb-2 ${
        selected
          ? 'bg-electric-600 border-electric-600'
          : 'bg-white border-gray-300'
      }`}
    >
      <Text className={`font-medium ${selected ? 'text-white' : 'text-gray-700'}`}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View className="flex-1 bg-white">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-lg font-semibold text-gray-900">Filters</Text>
          <TouchableOpacity onPress={clearAllFilters}>
            <Text className="text-electric-600 font-medium">Clear All</Text>
          </TouchableOpacity>
        </View>

        <ScrollView className="flex-1 p-4">
          {/* Price Range */}
          <FilterSection title="Price Range">
            <View className="space-y-4">
              <View>
                <Text className="text-sm text-gray-600 mb-2">
                  Maximum Price: {formatPrice(localFilters.priceMax || 15000000)}
                </Text>
                <Slider
                  style={{ width: '100%', height: 40 }}
                  minimumValue={2000000} // $20k
                  maximumValue={15000000} // $150k
                  step={500000} // $5k steps
                  value={localFilters.priceMax || 15000000}
                  onValueChange={(value) => updateLocalFilter('priceMax', value)}
                  minimumTrackTintColor="#22c55e"
                  maximumTrackTintColor="#d1d5db"
                  thumbStyle={{ backgroundColor: '#22c55e' }}
                />
              </View>
            </View>
          </FilterSection>

          {/* Make */}
          <FilterSection title="Make">
            <View className="flex-row flex-wrap">
              {VEHICLE_MAKES.slice(0, -1).map((make) => ( // Exclude "Other"
                <FilterOption
                  key={make}
                  label={make}
                  selected={localFilters.make === make}
                  onPress={() => updateLocalFilter('make', 
                    localFilters.make === make ? undefined : make
                  )}
                />
              ))}
            </View>
          </FilterSection>

          {/* Body Type */}
          <FilterSection title="Body Type">
            <View className="flex-row flex-wrap">
              {EV_BODY_TYPES.map((type) => (
                <FilterOption
                  key={type}
                  label={type.charAt(0).toUpperCase() + type.slice(1)}
                  selected={localFilters.bodyType === type}
                  onPress={() => updateLocalFilter('bodyType', 
                    localFilters.bodyType === type ? undefined : type
                  )}
                />
              ))}
            </View>
          </FilterSection>

          {/* Range */}
          <FilterSection title="Range">
            <View>
              <Text className="text-sm text-gray-600 mb-2">
                Minimum Range: {localFilters.rangeMin || 100} miles
              </Text>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={100}
                maximumValue={500}
                step={25}
                value={localFilters.rangeMin || 100}
                onValueChange={(value) => updateLocalFilter('rangeMin', value)}
                minimumTrackTintColor="#22c55e"
                maximumTrackTintColor="#d1d5db"
                thumbStyle={{ backgroundColor: '#22c55e' }}
              />
            </View>
          </FilterSection>

          {/* Efficiency */}
          <FilterSection title="Efficiency">
            <View>
              <Text className="text-sm text-gray-600 mb-2">
                Minimum Efficiency: {localFilters.efficiencyMin || 70} MPGe
              </Text>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={70}
                maximumValue={150}
                step={5}
                value={localFilters.efficiencyMin || 70}
                onValueChange={(value) => updateLocalFilter('efficiencyMin', value)}
                minimumTrackTintColor="#22c55e"
                maximumTrackTintColor="#d1d5db"
                thumbStyle={{ backgroundColor: '#22c55e' }}
              />
            </View>
          </FilterSection>

          {/* Performance */}
          <FilterSection title="Performance">
            <View>
              <Text className="text-sm text-gray-600 mb-2">
                Maximum 0-60 mph: {localFilters.accelerationMax || 10.0}s
              </Text>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={2.0}
                maximumValue={10.0}
                step={0.5}
                value={localFilters.accelerationMax || 10.0}
                onValueChange={(value) => updateLocalFilter('accelerationMax', value)}
                minimumTrackTintColor="#22c55e"
                maximumTrackTintColor="#d1d5db"
                thumbStyle={{ backgroundColor: '#22c55e' }}
              />
            </View>
          </FilterSection>

          {/* Charging Speed */}
          <FilterSection title="Charging Speed">
            <View>
              <Text className="text-sm text-gray-600 mb-2">
                Minimum DC Fast Charging: {localFilters.chargingSpeedMin || 50} kW
              </Text>
              <Slider
                style={{ width: '100%', height: 40 }}
                minimumValue={50}
                maximumValue={350}
                step={25}
                value={localFilters.chargingSpeedMin || 50}
                onValueChange={(value) => updateLocalFilter('chargingSpeedMin', value)}
                minimumTrackTintColor="#22c55e"
                maximumTrackTintColor="#d1d5db"
                thumbStyle={{ backgroundColor: '#22c55e' }}
              />
            </View>
          </FilterSection>

          {/* Special Categories */}
          <FilterSection title="Special Categories">
            <View className="flex-row flex-wrap">
              <FilterOption
                label="Featured"
                selected={localFilters.featured === 'true'}
                onPress={() => updateLocalFilter('featured', 
                  localFilters.featured === 'true' ? undefined : 'true'
                )}
              />
              <FilterOption
                label="Editor's Choice"
                selected={localFilters.editorChoice === 'true'}
                onPress={() => updateLocalFilter('editorChoice', 
                  localFilters.editorChoice === 'true' ? undefined : 'true'
                )}
              />
              <FilterOption
                label="Best Value"
                selected={localFilters.bestValue === 'true'}
                onPress={() => updateLocalFilter('bestValue', 
                  localFilters.bestValue === 'true' ? undefined : 'true'
                )}
              />
            </View>
          </FilterSection>
        </ScrollView>

        {/* Apply Button */}
        <View className="p-4 border-t border-gray-200">
          <TouchableOpacity
            onPress={applyFilters}
            className="bg-electric-600 rounded-xl py-4 items-center"
          >
            <Text className="text-white font-semibold text-lg">Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}
