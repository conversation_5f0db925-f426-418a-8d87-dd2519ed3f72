import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { EVModel } from '@greenmiles-ev/shared';
import { useComparison } from '../contexts/ComparisonContext';

interface ComparisonOverviewProps {
  models: EVModel[];
}

export function ComparisonOverview({ models }: ComparisonOverviewProps) {
  const { removeFromComparison } = useComparison();

  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA';
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A';
    return `${range} mi`;
  };

  const getImageSource = (model: EVModel) => {
    if (model.images && model.images.length > 0) {
      return { uri: model.images[0] };
    }
    return null;
  };

  const getBestInCategory = (category: 'price' | 'range' | 'efficiency' | 'acceleration') => {
    if (models.length === 0) return null;

    switch (category) {
      case 'price':
        return models.reduce((best, current) => {
          if (!current.price_msrp) return best;
          if (!best.price_msrp) return current;
          return current.price_msrp < best.price_msrp ? current : best;
        });
      case 'range':
        return models.reduce((best, current) => {
          if (!current.range_epa_miles) return best;
          if (!best.range_epa_miles) return current;
          return current.range_epa_miles > best.range_epa_miles ? current : best;
        });
      case 'efficiency':
        return models.reduce((best, current) => {
          if (!current.efficiency_mpge) return best;
          if (!best.efficiency_mpge) return current;
          return current.efficiency_mpge > best.efficiency_mpge ? current : best;
        });
      case 'acceleration':
        return models.reduce((best, current) => {
          if (!current.acceleration_0_60_mph) return best;
          if (!best.acceleration_0_60_mph) return current;
          return current.acceleration_0_60_mph < best.acceleration_0_60_mph ? current : best;
        });
      default:
        return models[0];
    }
  };

  const getModelRanking = (model: EVModel) => {
    let score = 0;
    let factors = 0;

    // Price ranking (lower is better)
    if (model.price_msrp) {
      const avgPrice = models.reduce((sum, m) => sum + (m.price_msrp || 0), 0) / models.length;
      score += model.price_msrp < avgPrice ? 2 : model.price_msrp === avgPrice ? 1 : 0;
      factors++;
    }

    // Range ranking (higher is better)
    if (model.range_epa_miles) {
      const avgRange = models.reduce((sum, m) => sum + (m.range_epa_miles || 0), 0) / models.length;
      score += model.range_epa_miles > avgRange ? 2 : model.range_epa_miles === avgRange ? 1 : 0;
      factors++;
    }

    // Efficiency ranking (higher is better)
    if (model.efficiency_mpge) {
      const avgEfficiency = models.reduce((sum, m) => sum + (m.efficiency_mpge || 0), 0) / models.length;
      score += model.efficiency_mpge > avgEfficiency ? 2 : model.efficiency_mpge === avgEfficiency ? 1 : 0;
      factors++;
    }

    return factors > 0 ? (score / (factors * 2)) * 100 : 50; // Percentage score
  };

  const VehicleCard = ({ model, index }: { model: EVModel; index: number }) => {
    const ranking = getModelRanking(model);
    const isTopRanked = ranking >= 75;
    const isGoodValue = ranking >= 50;

    return (
      <TouchableOpacity
        onPress={() => router.push(`/ev-models/${model.id}`)}
        className="bg-white rounded-xl shadow-sm border border-gray-100 mb-4 mx-4"
      >
        {/* Header with ranking */}
        <View className="flex-row items-center justify-between p-4 pb-2">
          <View className="flex-row items-center">
            <Text className="text-lg font-bold text-gray-400 mr-3">#{index + 1}</Text>
            <View>
              <Text className="text-lg font-semibold text-gray-900" numberOfLines={1}>
                {model.make} {model.model}
              </Text>
              {model.trim && (
                <Text className="text-sm text-gray-600" numberOfLines={1}>
                  {model.trim}
                </Text>
              )}
            </View>
          </View>
          
          <View className="flex-row items-center gap-2">
            {isTopRanked && (
              <View className="bg-green-100 rounded-full px-2 py-1">
                <Text className="text-green-700 text-xs font-medium">Top Pick</Text>
              </View>
            )}
            <TouchableOpacity
              onPress={() => removeFromComparison(model.id)}
              className="p-1"
            >
              <Ionicons name="close" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Image and key stats */}
        <View className="flex-row px-4 pb-4">
          <View className="relative mr-4">
            {getImageSource(model) ? (
              <Image
                source={getImageSource(model)}
                className="w-24 h-16 rounded-lg"
                resizeMode="cover"
              />
            ) : (
              <View className="w-24 h-16 rounded-lg bg-gray-200 items-center justify-center">
                <Ionicons name="car" size={24} color="#9ca3af" />
              </View>
            )}
          </View>

          <View className="flex-1">
            <Text className="text-xl font-bold text-electric-600 mb-2">
              {formatPrice(model.price_msrp)}
            </Text>
            
            <View className="flex-row justify-between">
              <View className="items-center">
                <Text className="text-xs text-gray-500">Range</Text>
                <Text className="font-semibold text-gray-900">
                  {formatRange(model.range_epa_miles)}
                </Text>
              </View>
              
              {model.efficiency_mpge && (
                <View className="items-center">
                  <Text className="text-xs text-gray-500">Efficiency</Text>
                  <Text className="font-semibold text-gray-900">
                    {model.efficiency_mpge} MPGe
                  </Text>
                </View>
              )}
              
              {model.acceleration_0_60_mph && (
                <View className="items-center">
                  <Text className="text-xs text-gray-500">0-60 mph</Text>
                  <Text className="font-semibold text-gray-900">
                    {model.acceleration_0_60_mph}s
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Ranking bar */}
        <View className="px-4 pb-4">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-sm text-gray-600">Overall Score</Text>
            <Text className="text-sm font-medium text-gray-900">{Math.round(ranking)}%</Text>
          </View>
          <View className="bg-gray-200 rounded-full h-2">
            <View
              className={`h-2 rounded-full ${
                isTopRanked ? 'bg-green-500' : isGoodValue ? 'bg-blue-500' : 'bg-gray-400'
              }`}
              style={{ width: `${ranking}%` }}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const CategoryWinner = ({ 
    title, 
    icon, 
    model, 
    value 
  }: { 
    title: string; 
    icon: keyof typeof Ionicons.glyphMap; 
    model: EVModel | null; 
    value: string;
  }) => {
    if (!model) return null;

    return (
      <View className="bg-white rounded-lg p-3 flex-1 shadow-sm">
        <View className="flex-row items-center mb-2">
          <Ionicons name={icon} size={16} color="#6b7280" />
          <Text className="text-xs text-gray-600 ml-1">{title}</Text>
        </View>
        <Text className="font-semibold text-gray-900 text-sm" numberOfLines={1}>
          {model.make} {model.model}
        </Text>
        <Text className="text-electric-600 font-bold text-lg">{value}</Text>
      </View>
    );
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Category Winners */}
      <View className="p-4">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Category Leaders</Text>
        
        <View className="flex-row gap-3 mb-3">
          <CategoryWinner
            title="Best Value"
            icon="cash"
            model={getBestInCategory('price')}
            value={formatPrice(getBestInCategory('price')?.price_msrp)}
          />
          <CategoryWinner
            title="Longest Range"
            icon="speedometer"
            model={getBestInCategory('range')}
            value={formatRange(getBestInCategory('range')?.range_epa_miles)}
          />
        </View>
        
        <View className="flex-row gap-3">
          <CategoryWinner
            title="Most Efficient"
            icon="leaf"
            model={getBestInCategory('efficiency')}
            value={`${getBestInCategory('efficiency')?.efficiency_mpge || 0} MPGe`}
          />
          <CategoryWinner
            title="Quickest"
            icon="flash"
            model={getBestInCategory('acceleration')}
            value={`${getBestInCategory('acceleration')?.acceleration_0_60_mph || 0}s`}
          />
        </View>
      </View>

      {/* Vehicle Rankings */}
      <View className="pb-20">
        <View className="px-4 mb-4">
          <Text className="text-lg font-semibold text-gray-900">Vehicle Rankings</Text>
          <Text className="text-gray-600 text-sm">
            Ranked by overall value considering price, range, and efficiency
          </Text>
        </View>
        
        {models
          .sort((a, b) => getModelRanking(b) - getModelRanking(a))
          .map((model, index) => (
            <VehicleCard key={model.id} model={model} index={index} />
          ))}
      </View>
    </ScrollView>
  );
}
