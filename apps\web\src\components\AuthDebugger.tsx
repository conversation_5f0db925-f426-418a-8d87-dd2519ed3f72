'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

export function AuthDebugger() {
  const { user, session, loading } = useAuth()
  const [logs, setLogs] = useState<string[]>([])

  useEffect(() => {
    const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString()
      setLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`])
    }

    addLog(`Auth state - Loading: ${loading}, User: ${user?.email || 'null'}, Session: ${session ? 'exists' : 'null'}`)
  }, [user, session, loading])

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm rounded-lg bg-black/80 p-4 text-xs text-white">
      <div className="mb-2 font-bold">Auth Debug</div>
      <div className="space-y-1">
        <div>Loading: {loading ? 'true' : 'false'}</div>
        <div>User: {user?.email || 'null'}</div>
        <div>Session: {session ? 'exists' : 'null'}</div>
      </div>
      <div className="mt-2 border-t border-gray-600 pt-2">
        <div className="mb-1 text-xs font-semibold">Recent logs:</div>
        <div className="max-h-32 overflow-y-auto text-xs">
          {logs.map((log, index) => (
            <div key={index} className="text-gray-300">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
