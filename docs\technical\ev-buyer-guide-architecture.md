# EV Buyer's Guide - Technical Architecture

## Overview

The EV Buyer's Guide is built as a comprehensive decision-making platform for electric vehicle buyers. It combines intelligent search, comparison tools, and decision matrices to help users make informed purchasing decisions.

## Architecture Components

### Frontend (Next.js 14)

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context + localStorage for persistence

### Backend (Supabase)

- **Database**: PostgreSQL with Supabase
- **Authentication**: Supabase Auth (future feature)
- **Storage**: Supabase Storage for images
- **Real-time**: Supabase Realtime (future feature)

### Key Features

#### 1. Intelligent Search System

**Components:**

- `IntelligentSearch.tsx` - Main search interface
- `EVModelSearch.tsx` - Basic search fallback
- `/api/ev-models/intelligent-search` - Semantic search API

**Features:**

- Natural language processing
- Intent recognition and semantic understanding
- Quick filters for common queries
- Search suggestions and autocomplete
- Recent and trending searches

**Technical Implementation:**

```typescript
// Search intent analysis
const analyzeSearchIntent = (query: string) => {
  // Pattern matching for budget, range, family needs, etc.
  // Returns structured intent data and suggested filters
};

// Semantic suggestions generation
const generateSemanticSuggestions = (intent: SearchIntent) => {
  // Context-aware suggestions based on user intent
};
```

#### 2. Smart Decision Filters

**Components:**

- `SmartDecisionFilters.tsx` - Main filter interface
- Buyer profiles for quick filter application
- Lifestyle quiz for personalized recommendations

**Buyer Profiles:**

```typescript
interface DecisionProfile {
  id: string;
  name: string;
  description: string;
  filters: Partial<EVModelFilters>;
  additionalCriteria?: {
    dailyMiles?: number;
    hasHomeCharging?: boolean;
    primaryUse?: string;
  };
}
```

**Lifestyle Analysis:**

- Budget range analysis
- Daily driving pattern assessment
- Family size and use case evaluation
- Charging infrastructure considerations

#### 3. Comparison System

**Components:**

- `ComparisonContext.tsx` - Global state management
- `ComparisonView.tsx` - Side-by-side comparison
- `ComparisonCard.tsx` - Individual model comparison cards

**State Management:**

```typescript
interface ComparisonState {
  comparisonList: EVModel[];
  maxComparisons: number;
  addToComparison: (model: EVModel) => void;
  removeFromComparison: (id: string) => void;
  clearComparison: () => void;
  isInComparison: (id: string) => boolean;
}
```

**Persistence:**

- localStorage for browser persistence
- Automatic state restoration on page load
- Cross-session comparison continuity

#### 4. Decision Matrix & Scoring

**Components:**

- `DecisionMatrix.tsx` - Priority weighting and scoring
- `RecommendationEngine.tsx` - Intelligent recommendations

**Scoring Algorithm:**

```typescript
interface ScoringWeights {
  price: number; // 0-100
  range: number; // 0-100
  efficiency: number; // 0-100
  performance: number; // 0-100
  charging: number; // 0-100
}

const calculateModelScore = (model: EVModel, weights: ScoringWeights) => {
  // Normalize each metric to 0-100 scale
  // Apply user weights
  // Return weighted total score
};
```

**Recommendation Logic:**

- Multi-criteria decision analysis
- Contextual reasoning generation
- Buyer profile matching
- Value proposition analysis

#### 5. Enhanced Model Details

**Components:**

- `EVModelTabs.tsx` - Tabbed navigation
- `EVModelOverview.tsx` - Key highlights and buyer insights
- `EVModelSpecs.tsx` - Comprehensive specifications
- `EVModelRealWorld.tsx` - Real-world performance data
- `EVModelCostAnalysis.tsx` - Total cost of ownership

**Real-World Analysis:**

- EPA vs actual performance comparison
- Environmental condition impacts
- Efficiency ratings and analysis
- Charging speed analysis
- Practical driving tips

**Cost Analysis:**

- Purchase price breakdown
- Financing calculator
- 5-year total cost of ownership
- EV vs gas vehicle comparison
- Incentive and rebate information

## Database Schema

### Core Tables

#### ev_models

```sql
CREATE TABLE ev_models (
  id UUID PRIMARY KEY,
  make VARCHAR NOT NULL,
  model VARCHAR NOT NULL,
  year INTEGER NOT NULL,
  trim VARCHAR,
  body_type VARCHAR,
  price_msrp INTEGER, -- in cents
  price_base INTEGER,
  production_status VARCHAR,

  -- Performance
  acceleration_0_60_mph DECIMAL,
  top_speed_mph INTEGER,
  motor_power_hp INTEGER,
  motor_torque_lb_ft INTEGER,
  drivetrain VARCHAR,

  -- Battery & Range
  battery_capacity_kwh DECIMAL,
  range_epa_miles INTEGER,
  range_wltp_miles INTEGER,
  range_real_world_miles INTEGER,
  efficiency_mpge INTEGER,
  energy_consumption_kwh_100mi DECIMAL,

  -- Charging
  charging_speed_dc_kw INTEGER,
  charging_speed_ac_kw INTEGER,
  charging_time_10_80_minutes INTEGER,
  charging_ports TEXT[],

  -- Physical
  length_inches DECIMAL,
  width_inches DECIMAL,
  height_inches DECIMAL,
  wheelbase_inches DECIMAL,
  ground_clearance_inches DECIMAL,
  curb_weight_lbs INTEGER,
  seating_capacity INTEGER,
  cargo_volume_cubic_ft DECIMAL,
  towing_capacity_lbs INTEGER,
  payload_capacity_lbs INTEGER,

  -- Features & Metadata
  features JSONB,
  safety_ratings JSONB,
  warranty_info JSONB,
  images TEXT[],
  market_regions TEXT[],

  -- Scoring
  is_featured BOOLEAN DEFAULT FALSE,
  popularity_score INTEGER DEFAULT 0,
  editor_choice BOOLEAN DEFAULT FALSE,
  best_value BOOLEAN DEFAULT FALSE,

  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### ev_manufacturers

```sql
CREATE TABLE ev_manufacturers (
  id UUID PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  logo_url VARCHAR,
  website_url VARCHAR,
  founded_year INTEGER,
  headquarters VARCHAR,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes and Performance

```sql
-- Search performance
CREATE INDEX idx_ev_models_search ON ev_models
USING GIN (to_tsvector('english', make || ' ' || model || ' ' || trim));

-- Filtering performance
CREATE INDEX idx_ev_models_filters ON ev_models
(make, body_type, production_status, price_msrp, range_epa_miles);

-- Sorting performance
CREATE INDEX idx_ev_models_popularity ON ev_models (popularity_score DESC);
CREATE INDEX idx_ev_models_price ON ev_models (price_msrp);
CREATE INDEX idx_ev_models_range ON ev_models (range_epa_miles DESC);
```

## API Endpoints

### Search & Filtering

- `GET /api/ev-models` - List and filter EV models
- `GET /api/ev-models/search` - Basic text search
- `GET /api/ev-models/intelligent-search` - Semantic search with intent analysis

### Model Details

- `GET /api/ev-models/[id]` - Individual model details
- `GET /api/ev-models/[id]/similar` - Similar model recommendations

### Comparison

- `POST /api/comparison/analyze` - Batch comparison analysis
- `GET /api/comparison/recommendations` - Personalized recommendations

## Testing Strategy

### Unit Tests

- Component testing with React Testing Library
- Hook testing for custom hooks
- Utility function testing
- API endpoint testing

### Integration Tests

- Search flow testing
- Comparison workflow testing
- Decision matrix functionality
- Filter application testing

### E2E Tests

- Complete user journeys
- Cross-browser compatibility
- Mobile responsiveness
- Performance testing

## Performance Optimizations

### Frontend

- Next.js App Router for optimal loading
- Image optimization with Next.js Image
- Component lazy loading
- Debounced search inputs
- Memoized expensive calculations

### Backend

- Database query optimization
- Proper indexing strategy
- Response caching where appropriate
- Pagination for large datasets

### Caching Strategy

- Browser caching for static assets
- API response caching
- localStorage for user preferences
- Service worker for offline capability (future)

## Security Considerations

### Data Protection

- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection

### Privacy

- No personal data collection without consent
- Local storage for user preferences
- Optional analytics with user consent

## Deployment

### Environment Setup

- Development: Local Next.js + Supabase
- Staging: Vercel + Supabase staging
- Production: Vercel + Supabase production

### CI/CD Pipeline

- GitHub Actions for automated testing
- Automated deployment to Vercel
- Database migration management
- Environment variable management

## Future Enhancements

### Planned Features

- User accounts and saved searches
- Real-time inventory integration
- Dealer integration and pricing
- Mobile app (React Native)
- Advanced AI recommendations
- Community reviews and ratings

### Technical Improvements

- GraphQL API implementation
- Real-time collaboration features
- Advanced analytics and insights
- Machine learning for better recommendations
- Progressive Web App features

## Testing Setup

### Test Configuration

**Jest Configuration** (`jest.config.js`):

```javascript
const nextJest = require("next/jest");

const createJestConfig = nextJest({
  dir: "./",
});

const customJestConfig = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/src/$1",
  },
  testEnvironment: "jest-environment-jsdom",
  collectCoverageFrom: [
    "src/**/*.{js,jsx,ts,tsx}",
    "!src/**/*.d.ts",
    "!src/app/layout.tsx",
    "!src/app/globals.css",
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};

module.exports = createJestConfig(customJestConfig);
```

**Test Setup** (`jest.setup.js`):

```javascript
import "@testing-library/jest-dom";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/test-path",
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock fetch
global.fetch = jest.fn();
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- ComparisonContext.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="Decision Matrix"
```

### Test Coverage Goals

- **Unit Tests**: 90%+ coverage for utilities and hooks
- **Component Tests**: 85%+ coverage for UI components
- **Integration Tests**: 80%+ coverage for user workflows
- **E2E Tests**: Critical user journeys covered

---

_Last updated: [Current Date]_
_Version: 1.0_
