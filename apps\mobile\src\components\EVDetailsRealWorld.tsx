import React from 'react';
import {
  View,
  Text,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { EVModel } from '@greenmiles-ev/shared';

interface EVDetailsRealWorldProps {
  model: EVModel;
}

export function EVDetailsRealWorld({ model }: EVDetailsRealWorldProps) {
  // Calculate real-world vs EPA differences
  const epaRange = model.range_epa_miles || 0;
  const realWorldRange = model.range_real_world_miles || epaRange * 0.85; // Estimate if not available
  const rangeDifference = epaRange - realWorldRange;
  const rangePercentage = epaRange > 0 ? ((realWorldRange / epaRange) * 100) : 0;

  // Calculate efficiency metrics
  const epaEfficiency = model.efficiency_mpge || 0;
  const realWorldEfficiency = epaEfficiency * 0.85; // Typical real-world reduction

  // Environmental impact calculations
  const annualMiles = 12000;
  const annualEnergyUse = epaEfficiency > 0 ? (annualMiles / epaEfficiency) * 33.7 : 0; // kWh
  const co2Reduction = (annualMiles / 28) * 19.6; // vs average gas car

  const formatRange = (range: number) => {
    return `${Math.round(range)} mi`;
  };

  const formatEfficiency = (efficiency: number) => {
    return `${Math.round(efficiency)} MPGe`;
  };

  const PerformanceCard = ({ 
    title, 
    epaValue, 
    realWorldValue, 
    unit, 
    icon 
  }: {
    title: string;
    epaValue: number;
    realWorldValue: number;
    unit: string;
    icon: keyof typeof Ionicons.glyphMap;
  }) => {
    const difference = epaValue - realWorldValue;
    const percentage = epaValue > 0 ? ((realWorldValue / epaValue) * 100) : 0;
    
    return (
      <View className="bg-white rounded-lg p-4 shadow-sm">
        <View className="flex-row items-center mb-3">
          <Ionicons name={icon} size={20} color="#6b7280" />
          <Text className="text-gray-900 font-medium ml-2">{title}</Text>
        </View>
        
        <View className="space-y-2">
          <View className="flex-row justify-between">
            <Text className="text-gray-600">EPA Rating</Text>
            <Text className="font-semibold text-gray-900">
              {Math.round(epaValue)} {unit}
            </Text>
          </View>
          
          <View className="flex-row justify-between">
            <Text className="text-gray-600">Real-World</Text>
            <Text className="font-semibold text-gray-900">
              {Math.round(realWorldValue)} {unit}
            </Text>
          </View>
          
          <View className="border-t border-gray-200 pt-2">
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-600">Difference</Text>
              <View className="flex-row items-center">
                <Text className={`font-medium ${difference > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                  {difference > 0 ? '-' : '+'}{Math.abs(Math.round(difference))} {unit}
                </Text>
                <Text className="text-gray-500 ml-2">
                  ({Math.round(percentage)}%)
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const DrivingTip = ({ 
    icon, 
    title, 
    description 
  }: {
    icon: keyof typeof Ionicons.glyphMap;
    title: string;
    description: string;
  }) => (
    <View className="flex-row items-start p-3 bg-gray-50 rounded-lg">
      <Ionicons name={icon} size={20} color="#22c55e" className="mt-1" />
      <View className="ml-3 flex-1">
        <Text className="font-medium text-gray-900 mb-1">{title}</Text>
        <Text className="text-gray-600 text-sm">{description}</Text>
      </View>
    </View>
  );

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Real-World Performance Overview */}
      <View className="bg-white m-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-2">
          Real-World Performance
        </Text>
        <Text className="text-gray-600 mb-4">
          How the {model.make} {model.model} performs in everyday driving conditions
        </Text>
        
        <View className="bg-blue-50 rounded-lg p-4">
          <View className="flex-row items-center mb-2">
            <Ionicons name="information-circle" size={20} color="#3b82f6" />
            <Text className="font-medium text-blue-900 ml-2">Real-World vs EPA</Text>
          </View>
          <Text className="text-blue-800 text-sm">
            Real-world performance typically differs from EPA ratings due to driving conditions, 
            weather, and individual driving habits.
          </Text>
        </View>
      </View>

      {/* Performance Comparison */}
      <View className="mx-4 mb-4 space-y-3">
        <PerformanceCard
          title="Driving Range"
          epaValue={epaRange}
          realWorldValue={realWorldRange}
          unit="miles"
          icon="speedometer"
        />
        
        <PerformanceCard
          title="Energy Efficiency"
          epaValue={epaEfficiency}
          realWorldValue={realWorldEfficiency}
          unit="MPGe"
          icon="leaf"
        />
      </View>

      {/* Factors Affecting Performance */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Factors Affecting Real-World Performance
        </Text>
        
        <View className="space-y-4">
          <View>
            <Text className="font-medium text-gray-900 mb-2">Weather Conditions</Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Cold Weather (< 32°F)</Text>
                <Text className="text-orange-600 font-medium">-20% to -40%</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Hot Weather (> 90°F)</Text>
                <Text className="text-orange-600 font-medium">-10% to -20%</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Mild Weather (65-75°F)</Text>
                <Text className="text-green-600 font-medium">Optimal</Text>
              </View>
            </View>
          </View>

          <View>
            <Text className="font-medium text-gray-900 mb-2">Driving Conditions</Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Highway (70+ mph)</Text>
                <Text className="text-orange-600 font-medium">-15% to -25%</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">City Driving</Text>
                <Text className="text-green-600 font-medium">+5% to +15%</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Mixed Driving</Text>
                <Text className="text-gray-600 font-medium">EPA Baseline</Text>
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* Charging Analysis */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Real-World Charging
        </Text>
        
        <View className="space-y-4">
          {model.charging_speed_dc_kw && (
            <View>
              <Text className="font-medium text-gray-900 mb-2">DC Fast Charging</Text>
              <View className="space-y-2">
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Peak Speed</Text>
                  <Text className="font-medium text-gray-900">{model.charging_speed_dc_kw} kW</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">10-80% Time</Text>
                  <Text className="font-medium text-gray-900">
                    {model.charging_time_10_80_minutes || 'N/A'} minutes
                  </Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Real-World Speed</Text>
                  <Text className="text-gray-600 font-medium">
                    {Math.round((model.charging_speed_dc_kw || 0) * 0.8)} kW avg
                  </Text>
                </View>
              </View>
            </View>
          )}

          <View className="bg-yellow-50 rounded-lg p-3">
            <View className="flex-row items-center mb-2">
              <Ionicons name="warning" size={16} color="#f59e0b" />
              <Text className="font-medium text-yellow-900 ml-2">Charging Note</Text>
            </View>
            <Text className="text-yellow-800 text-sm">
              Charging speeds vary based on battery temperature, state of charge, and charger condition. 
              Peak speeds are typically achieved between 10-50% charge.
            </Text>
          </View>
        </View>
      </View>

      {/* Environmental Impact */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Environmental Impact
        </Text>
        
        <View className="space-y-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="leaf" size={20} color="#22c55e" />
              <Text className="text-gray-700 ml-2">Annual CO₂ Reduction</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {Math.round(co2Reduction).toLocaleString()} lbs
            </Text>
          </View>
          
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="flash" size={20} color="#3b82f6" />
              <Text className="text-gray-700 ml-2">Annual Energy Use</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {Math.round(annualEnergyUse).toLocaleString()} kWh
            </Text>
          </View>
          
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="water" size={20} color="#06b6d4" />
              <Text className="text-gray-700 ml-2">Gas Equivalent Saved</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {Math.round(annualMiles / 28)} gallons/year
            </Text>
          </View>
        </View>
      </View>

      {/* Driving Tips */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Tips to Maximize Range
        </Text>
        
        <View className="space-y-3">
          <DrivingTip
            icon="speedometer"
            title="Moderate Speed"
            description="Maintain speeds between 45-65 mph for optimal efficiency. Higher speeds significantly reduce range."
          />
          
          <DrivingTip
            icon="snow"
            title="Pre-condition While Plugged In"
            description="Heat or cool your vehicle while connected to charger to preserve battery for driving."
          />
          
          <DrivingTip
            icon="car"
            title="Use Eco Mode"
            description="Enable eco driving modes to optimize acceleration and climate control for maximum range."
          />
          
          <DrivingTip
            icon="battery-charging"
            title="Regenerative Braking"
            description="Use maximum regenerative braking settings to recover energy when slowing down."
          />
          
          <DrivingTip
            icon="thermometer"
            title="Climate Control"
            description="Use seat and steering wheel heaters instead of cabin heating when possible to save energy."
          />
        </View>
      </View>

      {/* Bottom Padding for Floating Buttons */}
      <View className="h-20" />
    </ScrollView>
  );
}
