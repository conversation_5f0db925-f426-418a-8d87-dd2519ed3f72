import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { EVModel } from '@greenmiles-ev/shared';

interface EVDetailsCostAnalysisProps {
  model: EVModel;
}

export function EVDetailsCostAnalysis({ model }: EVDetailsCostAnalysisProps) {
  const [loanTerm, setLoanTerm] = useState(60); // months
  const [downPayment, setDownPayment] = useState(20); // percentage
  const [interestRate, setInterestRate] = useState(4.5); // percentage
  const [milesPerYear, setMilesPerYear] = useState(12000);
  const [electricityRate, setElectricityRate] = useState(0.13); // per kWh
  const [gasPrice, setGasPrice] = useState(3.50); // per gallon

  const basePrice = model.price_msrp ? model.price_msrp / 100 : 0;

  // Calculate financing
  const downPaymentAmount = basePrice * (downPayment / 100);
  const loanAmount = basePrice - downPaymentAmount;
  const monthlyRate = interestRate / 100 / 12;
  const monthlyPayment = loanAmount > 0 
    ? (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, loanTerm)) / 
      (Math.pow(1 + monthlyRate, loanTerm) - 1)
    : 0;

  // Calculate energy costs
  const annualEnergyConsumption = model.efficiency_mpge 
    ? (milesPerYear / model.efficiency_mpge) * 33.7 // kWh equivalent
    : 0;
  const annualElectricityCost = annualEnergyConsumption * electricityRate;

  // Calculate gas car comparison
  const averageGasMPG = 28; // Average new car MPG
  const annualGasConsumption = milesPerYear / averageGasMPG;
  const annualGasCost = annualGasConsumption * gasPrice;
  const annualFuelSavings = annualGasCost - annualElectricityCost;

  // 5-year total cost of ownership
  const fiveYearFinancing = monthlyPayment * Math.min(loanTerm, 60);
  const fiveYearElectricity = annualElectricityCost * 5;
  const fiveYearMaintenance = 2500; // Estimated EV maintenance over 5 years
  const fiveYearInsurance = 6000; // Estimated insurance over 5 years
  const fiveYearDepreciation = basePrice * 0.6; // 60% depreciation
  const fiveYearTotalCost = fiveYearFinancing + fiveYearElectricity + fiveYearMaintenance + fiveYearInsurance;

  // Gas car 5-year comparison
  const gasCarPrice = basePrice * 0.7; // Assume comparable gas car is 30% less
  const fiveYearGasCost = annualGasCost * 5;
  const fiveYearGasCarMaintenance = 8000; // Higher maintenance for gas cars
  const fiveYearGasCarTotal = gasCarPrice + fiveYearGasCost + fiveYearGasCarMaintenance + fiveYearInsurance;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const CostCard = ({ title, amount, subtitle, color = 'text-gray-900' }: {
    title: string;
    amount: number;
    subtitle?: string;
    color?: string;
  }) => (
    <View className="bg-gray-50 rounded-lg p-3 flex-1">
      <Text className="text-sm text-gray-600 mb-1">{title}</Text>
      <Text className={`text-lg font-bold ${color}`}>
        {formatCurrency(amount)}
      </Text>
      {subtitle && (
        <Text className="text-xs text-gray-500 mt-1">{subtitle}</Text>
      )}
    </View>
  );

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Purchase Price */}
      <View className="bg-white m-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Purchase Price</Text>
        
        <View className="space-y-3">
          <View className="flex-row justify-between">
            <Text className="text-gray-700">MSRP</Text>
            <Text className="font-semibold text-gray-900">
              {formatCurrency(basePrice)}
            </Text>
          </View>
          
          {model.price_base && model.price_base !== model.price_msrp && (
            <View className="flex-row justify-between">
              <Text className="text-gray-700">Base Price</Text>
              <Text className="font-semibold text-gray-900">
                {formatCurrency(model.price_base / 100)}
              </Text>
            </View>
          )}

          <View className="border-t border-gray-200 pt-3">
            <View className="flex-row justify-between">
              <Text className="text-gray-700">Federal Tax Credit</Text>
              <Text className="font-semibold text-green-600">-$7,500</Text>
            </View>
            <Text className="text-xs text-gray-500 mt-1">
              *Subject to eligibility and availability
            </Text>
          </View>
        </View>
      </View>

      {/* Financing Calculator */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Financing Calculator</Text>
        
        {/* Down Payment */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Down Payment</Text>
            <Text className="font-medium text-gray-900">
              {downPayment}% ({formatCurrency(downPaymentAmount)})
            </Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={0}
            maximumValue={50}
            step={5}
            value={downPayment}
            onValueChange={setDownPayment}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Loan Term */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Loan Term</Text>
            <Text className="font-medium text-gray-900">{loanTerm} months</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={24}
            maximumValue={84}
            step={12}
            value={loanTerm}
            onValueChange={setLoanTerm}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Interest Rate */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Interest Rate</Text>
            <Text className="font-medium text-gray-900">{interestRate.toFixed(1)}%</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={1.0}
            maximumValue={10.0}
            step={0.1}
            value={interestRate}
            onValueChange={setInterestRate}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Monthly Payment */}
        <View className="bg-electric-50 rounded-lg p-4">
          <Text className="text-center text-sm text-gray-600 mb-1">Estimated Monthly Payment</Text>
          <Text className="text-center text-2xl font-bold text-electric-600">
            {formatCurrency(monthlyPayment)}
          </Text>
        </View>
      </View>

      {/* Operating Costs */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Operating Costs</Text>
        
        {/* Miles per Year */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Annual Mileage</Text>
            <Text className="font-medium text-gray-900">{milesPerYear.toLocaleString()} miles</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={5000}
            maximumValue={25000}
            step={1000}
            value={milesPerYear}
            onValueChange={setMilesPerYear}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Electricity Rate */}
        <View className="mb-4">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-gray-700">Electricity Rate</Text>
            <Text className="font-medium text-gray-900">${electricityRate.toFixed(2)}/kWh</Text>
          </View>
          <Slider
            style={{ width: '100%', height: 40 }}
            minimumValue={0.08}
            maximumValue={0.30}
            step={0.01}
            value={electricityRate}
            onValueChange={setElectricityRate}
            minimumTrackTintColor="#22c55e"
            maximumTrackTintColor="#d1d5db"
            thumbStyle={{ backgroundColor: '#22c55e' }}
          />
        </View>

        {/* Annual Costs */}
        <View className="space-y-3">
          <View className="flex-row gap-3">
            <CostCard
              title="Annual Electricity"
              amount={annualElectricityCost}
              subtitle={`${annualEnergyConsumption.toFixed(0)} kWh/year`}
            />
            <CostCard
              title="Annual Savings"
              amount={annualFuelSavings}
              subtitle="vs. gas car"
              color="text-green-600"
            />
          </View>
        </View>
      </View>

      {/* 5-Year Total Cost of Ownership */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">5-Year Total Cost of Ownership</Text>
        
        <View className="space-y-4">
          {/* EV Costs */}
          <View>
            <Text className="font-medium text-gray-900 mb-3">This EV</Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Financing/Purchase</Text>
                <Text className="font-medium">{formatCurrency(fiveYearFinancing)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Electricity</Text>
                <Text className="font-medium">{formatCurrency(fiveYearElectricity)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Maintenance</Text>
                <Text className="font-medium">{formatCurrency(fiveYearMaintenance)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Insurance</Text>
                <Text className="font-medium">{formatCurrency(fiveYearInsurance)}</Text>
              </View>
              <View className="border-t border-gray-200 pt-2">
                <View className="flex-row justify-between">
                  <Text className="font-semibold text-gray-900">Total</Text>
                  <Text className="font-bold text-electric-600">{formatCurrency(fiveYearTotalCost)}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Gas Car Comparison */}
          <View className="bg-gray-50 rounded-lg p-3">
            <Text className="font-medium text-gray-900 mb-3">Comparable Gas Car</Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Purchase Price</Text>
                <Text className="font-medium">{formatCurrency(gasCarPrice)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Fuel</Text>
                <Text className="font-medium">{formatCurrency(fiveYearGasCost)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Maintenance</Text>
                <Text className="font-medium">{formatCurrency(fiveYearGasCarMaintenance)}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Insurance</Text>
                <Text className="font-medium">{formatCurrency(fiveYearInsurance)}</Text>
              </View>
              <View className="border-t border-gray-300 pt-2">
                <View className="flex-row justify-between">
                  <Text className="font-semibold text-gray-900">Total</Text>
                  <Text className="font-bold text-gray-700">{formatCurrency(fiveYearGasCarTotal)}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Savings Summary */}
          <View className="bg-green-50 rounded-lg p-4">
            <Text className="text-center text-sm text-gray-600 mb-1">5-Year Savings with EV</Text>
            <Text className="text-center text-2xl font-bold text-green-600">
              {formatCurrency(fiveYearGasCarTotal - fiveYearTotalCost)}
            </Text>
            <Text className="text-center text-xs text-gray-500 mt-1">
              Including fuel, maintenance, and tax incentives
            </Text>
          </View>
        </View>
      </View>

      {/* Environmental Impact */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Environmental Impact</Text>
        
        <View className="space-y-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="leaf" size={20} color="#22c55e" />
              <Text className="text-gray-700 ml-2">Annual CO₂ Reduction</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {Math.round(annualGasConsumption * 19.6)} lbs
            </Text>
          </View>
          
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="water" size={20} color="#3b82f6" />
              <Text className="text-gray-700 ml-2">Gallons of Gas Saved</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {Math.round(annualGasConsumption)}/year
            </Text>
          </View>
        </View>
      </View>

      {/* Bottom Padding for Floating Buttons */}
      <View className="h-20" />
    </ScrollView>
  );
}
