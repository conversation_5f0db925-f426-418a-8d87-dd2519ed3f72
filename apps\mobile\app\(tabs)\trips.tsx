import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function TripsScreen() {
  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Trip Summary */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
        <Text className="text-xl font-bold text-gray-900 mb-4">This Month</Text>
        
        <View className="flex-row justify-between mb-4">
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">847</Text>
            <Text className="text-sm text-gray-600">Miles Driven</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">23</Text>
            <Text className="text-sm text-gray-600">Trips</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">4.2</Text>
            <Text className="text-sm text-gray-600">mi/kWh</Text>
          </View>
        </View>

        <TouchableOpacity className="bg-electric-600 rounded-lg p-3 items-center">
          <View className="flex-row items-center">
            <Ionicons name="add" size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Plan New Trip</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Recent Trips */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Recent Trips</Text>
        
        <View className="bg-white rounded-xl shadow-sm">
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Home → Office</Text>
              <Text className="text-sm text-gray-600">Today, 8:30 AM</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">24.5 miles</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">35 min</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="battery-half-outline" size={16} color="#22c55e" />
                <Text className="text-sm text-electric-600 ml-1">-12%</Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Office → Grocery Store</Text>
              <Text className="text-sm text-gray-600">Yesterday, 6:15 PM</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">8.2 miles</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">18 min</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="battery-half-outline" size={16} color="#22c55e" />
                <Text className="text-sm text-electric-600 ml-1">-4%</Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Home → Airport</Text>
              <Text className="text-sm text-gray-600">3 days ago</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">42.8 miles</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">1h 5m</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="battery-half-outline" size={16} color="#22c55e" />
                <Text className="text-sm text-electric-600 ml-1">-18%</Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-medium text-gray-900">Weekend Road Trip</Text>
              <Text className="text-sm text-gray-600">1 week ago</Text>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="location-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">156.3 miles</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="time-outline" size={16} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">2h 45m</Text>
              </View>
              <View className="flex-row items-center">
                <Ionicons name="battery-half-outline" size={16} color="#22c55e" />
                <Text className="text-sm text-electric-600 ml-1">-65%</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Trip Analytics */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Analytics</Text>
        
        <View className="bg-white rounded-xl p-4 shadow-sm">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="font-medium text-gray-900">Efficiency Trend</Text>
            <TouchableOpacity>
              <Text className="text-sm text-electric-600">View Details</Text>
            </TouchableOpacity>
          </View>
          
          <View className="flex-row justify-between mb-3">
            <Text className="text-sm text-gray-600">Average Efficiency</Text>
            <Text className="text-sm font-medium text-gray-900">4.2 mi/kWh</Text>
          </View>
          
          <View className="flex-row justify-between mb-3">
            <Text className="text-sm text-gray-600">Carbon Saved</Text>
            <Text className="text-sm font-medium text-electric-600">127 lbs CO₂</Text>
          </View>
          
          <View className="flex-row justify-between">
            <Text className="text-sm text-gray-600">Money Saved</Text>
            <Text className="text-sm font-medium text-electric-600">$89.50</Text>
          </View>
        </View>
      </View>

      {/* Bottom Spacing */}
      <View className="h-6" />
    </ScrollView>
  );
}
