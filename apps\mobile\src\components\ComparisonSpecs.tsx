import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { EVModel } from '@greenmiles-ev/shared';

interface ComparisonSpecsProps {
  models: EVModel[];
}

interface SpecCategory {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  specs: Array<{
    label: string;
    key: keyof EVModel;
    unit?: string;
    formatter?: (value: any) => string;
  }>;
}

export function ComparisonSpecs({ models }: ComparisonSpecsProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['performance']);

  const formatValue = (value: any, unit?: string, formatter?: (value: any) => string) => {
    if (value === null || value === undefined) return 'N/A';
    if (formatter) return formatter(value);
    if (typeof value === 'number') {
      return unit ? `${value.toLocaleString()} ${unit}` : value.toLocaleString();
    }
    return unit ? `${value} ${unit}` : value;
  };

  const toggleCategory = (categoryTitle: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryTitle) 
        ? prev.filter(c => c !== categoryTitle)
        : [...prev, categoryTitle]
    );
  };

  const getBestValue = (spec: any, models: EVModel[]) => {
    const values = models.map(m => (m as any)[spec.key]).filter(v => v !== null && v !== undefined);
    if (values.length === 0) return null;

    // For price, lower is better; for most others, higher is better
    if (spec.key.includes('price') || spec.key.includes('acceleration')) {
      return Math.min(...values);
    }
    return Math.max(...values);
  };

  const specCategories: SpecCategory[] = [
    {
      title: 'Performance',
      icon: 'flash',
      specs: [
        { label: '0-60 mph', key: 'acceleration_0_60_mph', unit: 'seconds' },
        { label: 'Top Speed', key: 'top_speed_mph', unit: 'mph' },
        { label: 'Motor Power', key: 'motor_power_hp', unit: 'hp' },
        { label: 'Motor Torque', key: 'motor_torque_lb_ft', unit: 'lb-ft' },
        { label: 'Drivetrain', key: 'drivetrain', formatter: (v) => v?.toUpperCase() || 'N/A' },
      ],
    },
    {
      title: 'Range & Efficiency',
      icon: 'speedometer',
      specs: [
        { label: 'EPA Range', key: 'range_epa_miles', unit: 'miles' },
        { label: 'WLTP Range', key: 'range_wltp_miles', unit: 'miles' },
        { label: 'Real-World Range', key: 'range_real_world_miles', unit: 'miles' },
        { label: 'Efficiency (EPA)', key: 'efficiency_mpge', unit: 'MPGe' },
        { label: 'Battery Capacity', key: 'battery_capacity_kwh', unit: 'kWh' },
      ],
    },
    {
      title: 'Charging',
      icon: 'flash-outline',
      specs: [
        { label: 'DC Fast Charging', key: 'charging_speed_dc_kw', unit: 'kW' },
        { label: 'AC Charging', key: 'charging_speed_ac_kw', unit: 'kW' },
        { label: '10-80% Time', key: 'charging_time_10_80_minutes', unit: 'minutes' },
      ],
    },
    {
      title: 'Dimensions',
      icon: 'resize',
      specs: [
        { label: 'Length', key: 'length_inches', unit: 'inches' },
        { label: 'Width', key: 'width_inches', unit: 'inches' },
        { label: 'Height', key: 'height_inches', unit: 'inches' },
        { label: 'Wheelbase', key: 'wheelbase_inches', unit: 'inches' },
        { label: 'Curb Weight', key: 'curb_weight_lbs', unit: 'lbs' },
      ],
    },
    {
      title: 'Interior',
      icon: 'car',
      specs: [
        { label: 'Seating Capacity', key: 'seating_capacity', unit: 'seats' },
        { label: 'Cargo Volume', key: 'cargo_volume_cubic_ft', unit: 'cubic ft' },
        { label: 'Towing Capacity', key: 'towing_capacity_lbs', unit: 'lbs' },
      ],
    },
    {
      title: 'Pricing',
      icon: 'cash',
      specs: [
        { 
          label: 'MSRP', 
          key: 'price_msrp', 
          formatter: (v) => v ? `$${(v / 100).toLocaleString()}` : 'N/A' 
        },
        { 
          label: 'Base Price', 
          key: 'price_base', 
          formatter: (v) => v ? `$${(v / 100).toLocaleString()}` : 'N/A' 
        },
      ],
    },
  ];

  const SpecRow = ({ spec, models }: { spec: any; models: EVModel[] }) => {
    const bestValue = getBestValue(spec, models);
    const hasValidData = models.some(m => (m as any)[spec.key] !== null && (m as any)[spec.key] !== undefined);
    
    if (!hasValidData) return null;

    return (
      <View className="mb-4">
        <Text className="text-sm font-medium text-gray-700 mb-2">{spec.label}</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row gap-3">
            {models.map((model) => {
              const value = (model as any)[spec.key];
              const isBest = value === bestValue && value !== null && value !== undefined;
              
              return (
                <View 
                  key={model.id} 
                  className={`w-32 rounded-lg p-3 ${
                    isBest ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                  }`}
                >
                  <Text className="text-xs text-gray-500 mb-1" numberOfLines={1}>
                    {model.make} {model.model}
                  </Text>
                  <Text 
                    className={`font-semibold ${
                      isBest ? 'text-green-700' : 'text-gray-900'
                    }`}
                  >
                    {formatValue(value, spec.unit, spec.formatter)}
                  </Text>
                  {isBest && (
                    <View className="flex-row items-center mt-1">
                      <Ionicons name="trophy" size={12} color="#16a34a" />
                      <Text className="text-xs text-green-600 ml-1">Best</Text>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const CategorySection = ({ category }: { category: SpecCategory }) => {
    const isExpanded = expandedCategories.includes(category.title);
    const hasValidSpecs = category.specs.some(spec => 
      models.some(m => (m as any)[spec.key] !== null && (m as any)[spec.key] !== undefined)
    );

    if (!hasValidSpecs) return null;

    return (
      <View className="bg-white mx-4 mb-4 rounded-xl shadow-sm overflow-hidden">
        <TouchableOpacity
          onPress={() => toggleCategory(category.title)}
          className="flex-row items-center justify-between p-4 bg-gray-50"
        >
          <View className="flex-row items-center">
            <Ionicons name={category.icon} size={20} color="#6b7280" />
            <Text className="text-lg font-semibold text-gray-900 ml-3">
              {category.title}
            </Text>
          </View>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>

        {isExpanded && (
          <View className="p-4">
            {category.specs.map((spec, index) => (
              <SpecRow key={index} spec={spec} models={models} />
            ))}
          </View>
        )}
      </View>
    );
  };

  const ModelHeader = ({ model }: { model: EVModel }) => (
    <View className="w-32 items-center">
      <Text className="font-semibold text-gray-900 text-center text-sm" numberOfLines={2}>
        {model.make} {model.model}
      </Text>
      {model.trim && (
        <Text className="text-xs text-gray-600 text-center" numberOfLines={1}>
          {model.trim}
        </Text>
      )}
    </View>
  );

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Header with model names */}
      <View className="bg-white mx-4 mt-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Comparing {models.length} Vehicle{models.length > 1 ? 's' : ''}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row gap-3">
            {models.map((model) => (
              <ModelHeader key={model.id} model={model} />
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Specification Categories */}
      {specCategories.map((category, index) => (
        <CategorySection key={index} category={category} />
      ))}

      {/* Legend */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Legend</Text>
        <View className="space-y-2">
          <View className="flex-row items-center">
            <View className="w-4 h-4 bg-green-50 border border-green-200 rounded mr-3" />
            <Ionicons name="trophy" size={16} color="#16a34a" className="mr-2" />
            <Text className="text-gray-700">Best in category</Text>
          </View>
          <View className="flex-row items-center">
            <View className="w-4 h-4 bg-gray-50 rounded mr-3" />
            <Text className="text-gray-700 ml-5">Standard comparison</Text>
          </View>
        </View>
      </View>

      {/* Bottom Padding for Floating Buttons */}
      <View className="h-20" />
    </ScrollView>
  );
}
