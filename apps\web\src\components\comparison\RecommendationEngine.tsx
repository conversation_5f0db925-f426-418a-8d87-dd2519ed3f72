'use client'

import { useState, useMemo } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Lightbulb, 
  User, 
  DollarSign, 
  MapPin, 
  Zap, 
  Users,
  Star,
  TrendingUp,
  CheckCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatPrice, formatRange } from '@/shared/utils/ev-buyer-guide'
import type { EVModel } from '@/shared/types'

interface RecommendationEngineProps {
  models: EVModel[]
}

interface UserPreferences {
  budget: 'under-40k' | '40k-60k' | '60k-80k' | 'over-80k'
  primaryUse: 'daily-commute' | 'family-trips' | 'performance' | 'luxury' | 'efficiency'
  drivingDistance: 'under-50' | '50-100' | '100-200' | 'over-200'
  chargingAccess: 'home-only' | 'home-work' | 'public-frequent' | 'public-occasional'
  priorities: string[]
}

const budgetRanges = {
  'under-40k': { min: 0, max: 4000000, label: 'Under $40,000' },
  '40k-60k': { min: 4000000, max: 6000000, label: '$40,000 - $60,000' },
  '60k-80k': { min: 6000000, max: 8000000, label: '$60,000 - $80,000' },
  'over-80k': { min: 8000000, max: Infinity, label: 'Over $80,000' }
}

const useProfiles = {
  'daily-commute': { label: 'Daily Commuting', icon: <MapPin className="h-4 w-4" /> },
  'family-trips': { label: 'Family & Long Trips', icon: <Users className="h-4 w-4" /> },
  'performance': { label: 'Performance & Fun', icon: <Zap className="h-4 w-4" /> },
  'luxury': { label: 'Luxury & Comfort', icon: <Star className="h-4 w-4" /> },
  'efficiency': { label: 'Maximum Efficiency', icon: <TrendingUp className="h-4 w-4" /> }
}

const priorityOptions = [
  { id: 'low-cost', label: 'Low Purchase Price' },
  { id: 'long-range', label: 'Long Driving Range' },
  { id: 'fast-charging', label: 'Fast Charging' },
  { id: 'performance', label: 'Quick Acceleration' },
  { id: 'efficiency', label: 'High Efficiency' },
  { id: 'space', label: 'Interior Space' },
  { id: 'brand-reputation', label: 'Brand Reputation' },
  { id: 'latest-tech', label: 'Latest Technology' }
]

export function RecommendationEngine({ models }: RecommendationEngineProps) {
  const [preferences, setPreferences] = useState<UserPreferences>({
    budget: '40k-60k',
    primaryUse: 'daily-commute',
    drivingDistance: '50-100',
    chargingAccess: 'home-work',
    priorities: []
  })

  const [showResults, setShowResults] = useState(false)

  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setPreferences(prev => ({ ...prev, [key]: value }))
  }

  const togglePriority = (priorityId: string) => {
    setPreferences(prev => ({
      ...prev,
      priorities: prev.priorities.includes(priorityId)
        ? prev.priorities.filter(p => p !== priorityId)
        : [...prev.priorities, priorityId]
    }))
  }

  const recommendations = useMemo(() => {
    if (!showResults) return []

    const budgetRange = budgetRanges[preferences.budget]
    
    // Filter models by budget
    const budgetFilteredModels = models.filter(model => {
      const price = model.price_msrp || 0
      return price >= budgetRange.min && price <= budgetRange.max
    })

    // Score each model based on preferences
    const scoredModels = budgetFilteredModels.map(model => {
      let score = 0
      const reasons: string[] = []

      // Budget score (closer to middle of range = higher score)
      const price = model.price_msrp || 0
      const budgetMidpoint = (budgetRange.min + Math.min(budgetRange.max, 10000000)) / 2
      const budgetScore = Math.max(0, 100 - Math.abs(price - budgetMidpoint) / 100000)
      score += budgetScore * 0.2

      // Primary use scoring
      switch (preferences.primaryUse) {
        case 'daily-commute':
          if (model.efficiency_mpge && model.efficiency_mpge > 120) {
            score += 25
            reasons.push('Excellent efficiency for daily driving')
          }
          if (model.range_epa_miles && model.range_epa_miles > 250) {
            score += 15
            reasons.push('Good range for commuting')
          }
          break

        case 'family-trips':
          if (model.seating_capacity && model.seating_capacity >= 5) {
            score += 20
            reasons.push('Spacious seating for family')
          }
          if (model.range_epa_miles && model.range_epa_miles > 300) {
            score += 25
            reasons.push('Long range for family trips')
          }
          break

        case 'performance':
          if (model.acceleration_0_60_mph && model.acceleration_0_60_mph < 5) {
            score += 30
            reasons.push('Quick acceleration')
          }
          if (model.motor_power_hp && model.motor_power_hp > 300) {
            score += 15
            reasons.push('High power output')
          }
          break

        case 'luxury':
          if (price > 6000000) {
            score += 25
            reasons.push('Premium positioning')
          }
          if (model.is_featured) {
            score += 15
            reasons.push('Featured luxury model')
          }
          break

        case 'efficiency':
          if (model.efficiency_mpge && model.efficiency_mpge > 130) {
            score += 30
            reasons.push('Outstanding efficiency')
          }
          break
      }

      // Driving distance scoring
      const requiredRange = {
        'under-50': 150,
        '50-100': 200,
        '100-200': 300,
        'over-200': 400
      }[preferences.drivingDistance]

      if (model.range_epa_miles && model.range_epa_miles >= requiredRange) {
        score += 20
        reasons.push(`Range meets your ${preferences.drivingDistance} mile needs`)
      }

      // Charging access scoring
      if (preferences.chargingAccess === 'public-frequent' || preferences.chargingAccess === 'public-occasional') {
        if (model.charging_speed_dc_kw && model.charging_speed_dc_kw > 150) {
          score += 15
          reasons.push('Fast public charging capability')
        }
      }

      // Priority scoring
      preferences.priorities.forEach(priority => {
        switch (priority) {
          case 'low-cost':
            if (price < budgetMidpoint) {
              score += 10
              reasons.push('Good value for money')
            }
            break
          case 'long-range':
            if (model.range_epa_miles && model.range_epa_miles > 350) {
              score += 15
              reasons.push('Exceptional driving range')
            }
            break
          case 'fast-charging':
            if (model.charging_speed_dc_kw && model.charging_speed_dc_kw > 200) {
              score += 15
              reasons.push('Very fast charging')
            }
            break
          case 'performance':
            if (model.acceleration_0_60_mph && model.acceleration_0_60_mph < 4.5) {
              score += 15
              reasons.push('Sports car performance')
            }
            break
          case 'efficiency':
            if (model.efficiency_mpge && model.efficiency_mpge > 125) {
              score += 15
              reasons.push('High energy efficiency')
            }
            break
        }
      })

      // Bonus for featured/award models
      if (model.is_featured) score += 5
      if (model.best_value) score += 10
      if (model.editor_choice) score += 15

      return {
        model,
        score: Math.min(100, score),
        reasons: reasons.slice(0, 3) // Limit to top 3 reasons
      }
    })

    return scoredModels
      .sort((a, b) => b.score - a.score)
      .slice(0, 3) // Top 3 recommendations
  }, [models, preferences, showResults])

  const handleGetRecommendations = () => {
    setShowResults(true)
  }

  const resetPreferences = () => {
    setPreferences({
      budget: '40k-60k',
      primaryUse: 'daily-commute',
      drivingDistance: '50-100',
      chargingAccess: 'home-work',
      priorities: []
    })
    setShowResults(false)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-electric-600" />
            Smart Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-400">
            Answer a few questions to get personalized EV recommendations based on your needs and preferences.
          </p>
        </CardContent>
      </Card>

      {/* Preference Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Your Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Budget */}
          <div className="space-y-3">
            <Label className="text-base font-medium">What's your budget?</Label>
            <RadioGroup
              value={preferences.budget}
              onValueChange={(value) => updatePreference('budget', value as any)}
            >
              {Object.entries(budgetRanges).map(([key, range]) => (
                <div key={key} className="flex items-center space-x-2">
                  <RadioGroupItem value={key} id={key} />
                  <Label htmlFor={key}>{range.label}</Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Primary Use */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Primary use case?</Label>
            <RadioGroup
              value={preferences.primaryUse}
              onValueChange={(value) => updatePreference('primaryUse', value as any)}
            >
              {Object.entries(useProfiles).map(([key, profile]) => (
                <div key={key} className="flex items-center space-x-2">
                  <RadioGroupItem value={key} id={key} />
                  <Label htmlFor={key} className="flex items-center gap-2">
                    {profile.icon}
                    {profile.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Daily Driving Distance */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Daily driving distance?</Label>
            <RadioGroup
              value={preferences.drivingDistance}
              onValueChange={(value) => updatePreference('drivingDistance', value as any)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="under-50" id="under-50" />
                <Label htmlFor="under-50">Under 50 miles</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="50-100" id="50-100" />
                <Label htmlFor="50-100">50-100 miles</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="100-200" id="100-200" />
                <Label htmlFor="100-200">100-200 miles</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="over-200" id="over-200" />
                <Label htmlFor="over-200">Over 200 miles</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Priorities */}
          <div className="space-y-3">
            <Label className="text-base font-medium">What's most important to you? (Select all that apply)</Label>
            <div className="grid grid-cols-2 gap-3">
              {priorityOptions.map(option => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.id}
                    checked={preferences.priorities.includes(option.id)}
                    onCheckedChange={() => togglePriority(option.id)}
                  />
                  <Label htmlFor={option.id} className="text-sm">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            <Button onClick={handleGetRecommendations} className="flex-1">
              Get My Recommendations
            </Button>
            <Button variant="outline" onClick={resetPreferences}>
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {showResults && recommendations.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Your Personalized Recommendations</h3>
          
          {recommendations.map((rec, index) => (
            <Card key={rec.model.id} className={cn(
              'transition-all',
              index === 0 && 'ring-2 ring-electric-500 bg-electric-50 dark:bg-electric-950'
            )}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      {index === 0 && <Star className="h-5 w-5 text-amber-500" />}
                      <h4 className="font-semibold text-lg">
                        {rec.model.make} {rec.model.model}
                      </h4>
                      <Badge variant={index === 0 ? 'default' : 'secondary'}>
                        #{index + 1} Match
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400">
                      {formatPrice(rec.model.price_msrp)} • {formatRange(rec.model.range_epa_miles)}
                    </p>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-2xl font-bold text-electric-600">
                      {Math.round(rec.score)}%
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Match Score
                    </div>
                  </div>
                </div>

                {/* Reasons */}
                <div className="space-y-2">
                  <h5 className="font-medium text-sm">Why this is a good match:</h5>
                  {rec.reasons.map((reason, idx) => (
                    <div key={idx} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 shrink-0" />
                      <span>{reason}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {showResults && recommendations.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-gray-600 dark:text-gray-400">
              No models match your current criteria. Try adjusting your budget or preferences.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
