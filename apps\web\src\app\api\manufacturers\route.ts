import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('include_inactive') === 'true'

    // Build query
    let query = supabase
      .from('ev_manufacturers')
      .select(`
        id,
        name,
        logo_url,
        website_url,
        headquarters_country,
        founded_year,
        description,
        is_active
      `)

    // Filter by active status unless explicitly including inactive
    if (!includeInactive) {
      query = query.eq('is_active', true)
    }

    // Order by name
    query = query.order('name', { ascending: true })

    const { data: manufacturers, error } = await query

    if (error) {
      console.error('Error fetching manufacturers:', error)
      return NextResponse.json(
        { error: 'Failed to fetch manufacturers' },
        { status: 500 }
      )
    }

    // Get model counts for each manufacturer
    const manufacturerIds = manufacturers?.map(m => m.id) || []
    
    if (manufacturerIds.length > 0) {
      const { data: modelCounts } = await supabase
        .from('ev_models')
        .select('manufacturer_id')
        .in('manufacturer_id', manufacturerIds)
        .eq('production_status', 'current')

      // Count models per manufacturer
      const countsByManufacturer = modelCounts?.reduce((acc, model) => {
        acc[model.manufacturer_id] = (acc[model.manufacturer_id] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      // Add model counts to manufacturers
      const manufacturersWithCounts = manufacturers?.map(manufacturer => ({
        ...manufacturer,
        model_count: countsByManufacturer[manufacturer.id] || 0
      }))

      return NextResponse.json({
        data: manufacturersWithCounts || []
      })
    }

    return NextResponse.json({
      data: manufacturers || []
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
