import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native'
import { Link, useRouter } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { useAuth } from '../../src/contexts/AuthContext'

export default function SignInScreen() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const { signIn } = useAuth()
  const router = useRouter()

  const handleSignIn = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields')
      return
    }

    setLoading(true)
    try {
      console.log('Starting sign in process...')

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Sign in timeout - please try again')), 15000)
      )

      const signInPromise = signIn(email, password)

      const { error } = (await Promise.race([signInPromise, timeoutPromise])) as any

      if (error) {
        console.error('Sign in failed:', error)
        Alert.alert('Sign In Failed', error.message)
        setLoading(false)
      } else {
        console.log('Sign in successful, redirecting...')
        // Don't set loading false here - let the auth context handle it
        // Add a small delay to ensure auth state is updated
        setTimeout(() => {
          router.replace('/(tabs)')
        }, 500)
      }
    } catch (err: any) {
      console.error('Sign in error:', err)
      Alert.alert('Error', err.message || 'An unexpected error occurred')
      setLoading(false)
    }
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-electric-50"
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="px-6">
        {/* Header */}
        <View className="flex-1 justify-center py-12">
          <View className="items-center mb-8">
            <View className="flex-row items-center mb-4">
              <Ionicons name="flash" size={32} color="#22c55e" />
              <Text className="text-3xl font-bold text-gray-900 ml-2">GreenMilesEV</Text>
            </View>
            <Text className="text-2xl font-bold text-gray-900 mb-2">Welcome back</Text>
            <Text className="text-gray-600 text-center">Sign in to your account to continue</Text>
          </View>

          {/* Sign In Form */}
          <View className="bg-white rounded-2xl p-6 shadow-lg">
            <Text className="text-xl font-bold text-gray-900 mb-6">Sign In</Text>

            {/* Email Input */}
            <View className="mb-4">
              <Text className="text-sm font-medium text-gray-700 mb-2">Email</Text>
              <View className="bg-gray-50 rounded-lg px-4 py-3 border border-gray-200">
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                  className="text-gray-900 text-base"
                />
              </View>
            </View>

            {/* Password Input */}
            <View className="mb-6">
              <Text className="text-sm font-medium text-gray-700 mb-2">Password</Text>
              <View className="bg-gray-50 rounded-lg px-4 py-3 border border-gray-200 flex-row items-center">
                <TextInput
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Enter your password"
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                  className="flex-1 text-gray-900 text-base"
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  disabled={loading}
                  className="ml-2"
                >
                  <Ionicons name={showPassword ? 'eye-off' : 'eye'} size={20} color="#6b7280" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Forgot Password Link */}
            <View className="mb-6">
              <Link href="/auth/forgot-password" asChild>
                <TouchableOpacity disabled={loading}>
                  <Text className="text-electric-600 text-sm font-medium text-right">
                    Forgot password?
                  </Text>
                </TouchableOpacity>
              </Link>
            </View>

            {/* Sign In Button */}
            <TouchableOpacity
              onPress={handleSignIn}
              disabled={loading}
              className={`bg-electric-600 rounded-lg py-4 items-center ${
                loading ? 'opacity-50' : ''
              }`}
            >
              {loading ? (
                <View className="flex-row items-center">
                  <ActivityIndicator color="white" size="small" />
                  <Text className="text-white font-semibold ml-2">Signing in...</Text>
                </View>
              ) : (
                <Text className="text-white font-semibold text-base">Sign In</Text>
              )}
            </TouchableOpacity>

            {/* Divider */}
            <View className="flex-row items-center my-6">
              <View className="flex-1 h-px bg-gray-200" />
              <Text className="mx-4 text-gray-500 text-sm">Or continue with</Text>
              <View className="flex-1 h-px bg-gray-200" />
            </View>

            {/* Social Sign In Buttons */}
            <View className="flex-row space-x-4">
              <TouchableOpacity
                disabled={loading}
                className="flex-1 bg-white border border-gray-200 rounded-lg py-3 items-center flex-row justify-center"
              >
                <Ionicons name="logo-google" size={20} color="#4285F4" />
                <Text className="text-gray-700 font-medium ml-2">Google</Text>
              </TouchableOpacity>

              <TouchableOpacity
                disabled={loading}
                className="flex-1 bg-white border border-gray-200 rounded-lg py-3 items-center flex-row justify-center"
              >
                <Ionicons name="logo-github" size={20} color="#333" />
                <Text className="text-gray-700 font-medium ml-2">GitHub</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Sign Up Link */}
          <View className="flex-row justify-center mt-6">
            <Text className="text-gray-600">Don't have an account? </Text>
            <Link href="/auth/signup" asChild>
              <TouchableOpacity disabled={loading}>
                <Text className="text-electric-600 font-medium">Sign up</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}
