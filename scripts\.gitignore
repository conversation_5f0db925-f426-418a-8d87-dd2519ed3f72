# Dependencies
node_modules/

# Build outputs
dist/
build/
lib/

# Environment variables
.env
.env.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Generated files
generated/
output/

# Temporary files
tmp/
temp/
.tmp/

# Cache
.cache/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Compiled scripts
*.pyc
*.pyo
__pycache__/

# Shell script outputs
*.out
*.err
