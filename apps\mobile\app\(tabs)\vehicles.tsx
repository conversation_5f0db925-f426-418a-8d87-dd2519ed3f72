import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function VehiclesScreen() {
  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Current Vehicle */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-xl font-bold text-gray-900">My Tesla Model 3</Text>
          <TouchableOpacity>
            <Ionicons name="settings-outline" size={24} color="#6b7280" />
          </TouchableOpacity>
        </View>
        
        <View className="bg-electric-50 rounded-lg p-4 mb-4">
          <View className="flex-row items-center justify-between">
            <View>
              <Text className="text-3xl font-bold text-electric-600">85%</Text>
              <Text className="text-sm text-gray-600">Battery Level</Text>
            </View>
            <View className="items-end">
              <Text className="text-lg font-semibold text-gray-900">247 miles</Text>
              <Text className="text-sm text-gray-600">Estimated Range</Text>
            </View>
          </View>
        </View>

        <View className="flex-row justify-between">
          <View className="items-center">
            <Text className="text-lg font-semibold text-gray-900">2021</Text>
            <Text className="text-sm text-gray-600">Year</Text>
          </View>
          <View className="items-center">
            <Text className="text-lg font-semibold text-gray-900">75 kWh</Text>
            <Text className="text-sm text-gray-600">Battery</Text>
          </View>
          <View className="items-center">
            <Text className="text-lg font-semibold text-gray-900">358 mi</Text>
            <Text className="text-sm text-gray-600">Max Range</Text>
          </View>
        </View>
      </View>

      {/* Vehicle Actions */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Vehicle Controls</Text>
        <View className="bg-white rounded-xl shadow-sm">
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="thermometer-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Climate Control</Text>
                <Text className="text-sm text-gray-600">Pre-condition your vehicle</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <Ionicons name="lock-closed-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Lock/Unlock</Text>
                <Text className="text-sm text-gray-600">Remote vehicle access</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity className="p-4">
            <View className="flex-row items-center">
              <Ionicons name="flash-outline" size={24} color="#22c55e" />
              <View className="ml-3 flex-1">
                <Text className="font-medium text-gray-900">Start Charging</Text>
                <Text className="text-sm text-gray-600">Begin charging session</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#6b7280" />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Maintenance */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Maintenance</Text>
        <View className="bg-white rounded-xl p-4 shadow-sm">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="font-medium text-gray-900">Next Service</Text>
            <Text className="text-sm text-electric-600">In 2,500 miles</Text>
          </View>
          <View className="bg-gray-200 rounded-full h-2 mb-2">
            <View className="bg-electric-600 h-2 rounded-full" style={{ width: '75%' }} />
          </View>
          <Text className="text-sm text-gray-600">Tire rotation and inspection</Text>
        </View>
      </View>

      {/* Add Vehicle Button */}
      <View className="mx-4 mt-6 mb-6">
        <TouchableOpacity className="bg-electric-600 rounded-xl p-4 items-center">
          <View className="flex-row items-center">
            <Ionicons name="add" size={24} color="white" />
            <Text className="text-white font-semibold ml-2">Add Another Vehicle</Text>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
