import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { EVModel } from '@greenmiles-ev/shared';

interface EVModelFilters {
  search?: string;
  make?: string;
  bodyType?: string;
  priceMin?: string;
  priceMax?: string;
  rangeMin?: string;
  rangeMax?: string;
  efficiencyMin?: string;
  accelerationMax?: string;
  chargingSpeedMin?: string;
  seatingMin?: string;
  featured?: string;
  editorChoice?: string;
  bestValue?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UseEVModelsReturn {
  evModels: EVModel[] | null;
  loading: boolean;
  error: string | null;
  filters: EVModelFilters;
  updateFilters: (newFilters: Partial<EVModelFilters>) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
  refetch: () => Promise<void>;
}

const DEFAULT_FILTERS: EVModelFilters = {
  sortBy: 'popularity_score',
  sortOrder: 'desc',
};

export function useEVModels(): UseEVModelsReturn {
  const [evModels, setEvModels] = useState<EVModel[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<EVModelFilters>(DEFAULT_FILTERS);

  const buildQuery = useCallback(() => {
    let query = supabase
      .from('ev_models')
      .select(`
        *,
        ev_manufacturers!inner(
          name,
          logo_url
        )
      `)
      .eq('production_status', 'current');

    // Apply filters
    if (filters.search) {
      query = query.or(`make.ilike.%${filters.search}%,model.ilike.%${filters.search}%,trim.ilike.%${filters.search}%`);
    }

    if (filters.make) {
      query = query.eq('make', filters.make);
    }

    if (filters.bodyType) {
      query = query.eq('body_type', filters.bodyType);
    }

    if (filters.priceMin) {
      query = query.gte('price_msrp', parseInt(filters.priceMin));
    }

    if (filters.priceMax) {
      query = query.lte('price_msrp', parseInt(filters.priceMax));
    }

    if (filters.rangeMin) {
      query = query.gte('range_epa_miles', parseInt(filters.rangeMin));
    }

    if (filters.rangeMax) {
      query = query.lte('range_epa_miles', parseInt(filters.rangeMax));
    }

    if (filters.efficiencyMin) {
      query = query.gte('efficiency_mpge', parseInt(filters.efficiencyMin));
    }

    if (filters.accelerationMax) {
      query = query.lte('acceleration_0_60_mph', parseFloat(filters.accelerationMax));
    }

    if (filters.chargingSpeedMin) {
      query = query.gte('charging_speed_dc_kw', parseInt(filters.chargingSpeedMin));
    }

    if (filters.seatingMin) {
      query = query.gte('seating_capacity', parseInt(filters.seatingMin));
    }

    if (filters.featured === 'true') {
      query = query.eq('is_featured', true);
    }

    if (filters.editorChoice === 'true') {
      query = query.eq('editor_choice', true);
    }

    if (filters.bestValue === 'true') {
      query = query.eq('best_value', true);
    }

    // Apply sorting
    const sortBy = filters.sortBy || 'popularity_score';
    const sortOrder = filters.sortOrder || 'desc';
    const ascending = sortOrder === 'asc';

    query = query.order(sortBy, { ascending });

    // Limit results for mobile performance
    query = query.limit(50);

    return query;
  }, [filters]);

  const fetchEVModels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const query = buildQuery();
      const { data, error: queryError } = await query;

      if (queryError) {
        throw queryError;
      }

      // Transform data to include manufacturer info
      const transformedData = data?.map((item: any) => ({
        ...item,
        manufacturer: item.ev_manufacturers,
      })) || [];

      setEvModels(transformedData);
    } catch (err) {
      console.error('Error fetching EV models:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch EV models');
    } finally {
      setLoading(false);
    }
  }, [buildQuery]);

  const updateFilters = useCallback((newFilters: Partial<EVModelFilters>) => {
    setFilters(prev => {
      const updated = { ...prev };
      
      // Update or remove filters
      Object.entries(newFilters).forEach(([key, value]) => {
        if (value === undefined || value === null || value === '') {
          delete updated[key as keyof EVModelFilters];
        } else {
          (updated as any)[key] = value;
        }
      });

      return updated;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, []);

  const hasActiveFilters = Object.keys(filters).some(
    key => key !== 'sortBy' && key !== 'sortOrder' && filters[key as keyof EVModelFilters]
  );

  const refetch = useCallback(async () => {
    await fetchEVModels();
  }, [fetchEVModels]);

  // Fetch data when filters change
  useEffect(() => {
    fetchEVModels();
  }, [fetchEVModels]);

  return {
    evModels,
    loading,
    error,
    filters,
    updateFilters,
    clearFilters,
    hasActiveFilters,
    refetch,
  };
}
