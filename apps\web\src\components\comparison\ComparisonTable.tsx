'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown, 
  ChevronUp, 
  Crown, 
  TrendingUp, 
  TrendingDown,
  Minus
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatPrice, formatRange, formatChargingTime, formatAcceleration } from '@/shared/utils/ev-buyer-guide'
import type { EVModel } from '@/shared/types'

interface ComparisonTableProps {
  models: EVModel[]
}

interface ComparisonRow {
  category: string
  label: string
  getValue: (model: EVModel) => string | number | null
  format?: (value: any) => string
  higherIsBetter?: boolean
  unit?: string
}

const comparisonRows: ComparisonRow[] = [
  // Pricing
  { category: 'Pricing', label: 'MSRP', getValue: (m) => m.price_msrp, format: formatPrice, higherIsBetter: false },
  { category: 'Pricing', label: 'Base Price', getValue: (m) => m.price_base, format: formatPrice, higherIsBetter: false },
  
  // Performance
  { category: 'Performance', label: '0-60 mph', getValue: (m) => m.acceleration_0_60_mph, format: formatAcceleration, higherIsBetter: false },
  { category: 'Performance', label: 'Top Speed', getValue: (m) => m.top_speed_mph, format: (v) => v ? `${v} mph` : 'N/A', higherIsBetter: true },
  { category: 'Performance', label: 'Motor Power', getValue: (m) => m.motor_power_hp, format: (v) => v ? `${v} hp` : 'N/A', higherIsBetter: true },
  { category: 'Performance', label: 'Drivetrain', getValue: (m) => m.drivetrain, format: (v) => v ? v.toUpperCase() : 'N/A' },
  
  // Range & Efficiency
  { category: 'Range & Efficiency', label: 'EPA Range', getValue: (m) => m.range_epa_miles, format: formatRange, higherIsBetter: true },
  { category: 'Range & Efficiency', label: 'Real-World Range', getValue: (m) => m.range_real_world_miles, format: formatRange, higherIsBetter: true },
  { category: 'Range & Efficiency', label: 'Efficiency', getValue: (m) => m.efficiency_mpge, format: (v) => v ? `${v} MPGe` : 'N/A', higherIsBetter: true },
  
  // Battery & Charging
  { category: 'Battery & Charging', label: 'Battery Capacity', getValue: (m) => m.battery_capacity_kwh, format: (v) => v ? `${v} kWh` : 'N/A', higherIsBetter: true },
  { category: 'Battery & Charging', label: 'DC Fast Charging', getValue: (m) => m.charging_speed_dc_kw, format: (v) => v ? `${v} kW` : 'N/A', higherIsBetter: true },
  { category: 'Battery & Charging', label: 'AC Charging', getValue: (m) => m.charging_speed_ac_kw, format: (v) => v ? `${v} kW` : 'N/A', higherIsBetter: true },
  { category: 'Battery & Charging', label: '10-80% Charge Time', getValue: (m) => m.charging_time_10_80_minutes, format: formatChargingTime, higherIsBetter: false },
  
  // Dimensions
  { category: 'Dimensions', label: 'Seating Capacity', getValue: (m) => m.seating_capacity, format: (v) => v ? `${v} seats` : 'N/A', higherIsBetter: true },
  { category: 'Dimensions', label: 'Curb Weight', getValue: (m) => m.curb_weight_lbs, format: (v) => v ? `${v.toLocaleString()} lbs` : 'N/A', higherIsBetter: false },
  
  // Other
  { category: 'Other', label: 'Production Status', getValue: (m) => m.production_status, format: (v) => v ? v.charAt(0).toUpperCase() + v.slice(1) : 'N/A' },
  { category: 'Other', label: 'Popularity Score', getValue: (m) => m.popularity_score, format: (v) => v ? `${v}/100` : 'N/A', higherIsBetter: true },
]

export function ComparisonTable({ models }: ComparisonTableProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['Pricing', 'Performance', 'Range & Efficiency'])
  )

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const getBestValueIndex = (row: ComparisonRow): number | null => {
    const values = models.map(model => {
      const value = row.getValue(model)
      return typeof value === 'number' ? value : null
    })

    const validValues = values.filter(v => v !== null) as number[]
    if (validValues.length === 0) return null

    const bestValue = row.higherIsBetter 
      ? Math.max(...validValues)
      : Math.min(...validValues)

    return values.findIndex(v => v === bestValue)
  }

  const getComparisonIcon = (value: any, bestIndex: number | null, currentIndex: number) => {
    if (bestIndex === null || value === null) return <Minus className="h-4 w-4 text-gray-400" />
    
    if (currentIndex === bestIndex) {
      return <Crown className="h-4 w-4 text-amber-500" />
    }
    
    return null
  }

  const categories = [...new Set(comparisonRows.map(row => row.category))]

  return (
    <div className="space-y-6">
      {categories.map(category => {
        const categoryRows = comparisonRows.filter(row => row.category === category)
        const isExpanded = expandedCategories.has(category)

        return (
          <Card key={category}>
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleCategory(category)}
            >
              <CardTitle className="flex items-center justify-between">
                <span>{category}</span>
                {isExpanded ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </CardTitle>
            </CardHeader>

            {isExpanded && (
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium">Specification</th>
                        {models.map(model => (
                          <th key={model.id} className="text-center py-3 px-4 font-medium min-w-[150px]">
                            {model.make} {model.model}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {categoryRows.map(row => {
                        const bestIndex = getBestValueIndex(row)
                        
                        return (
                          <tr key={row.label} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                              {row.label}
                            </td>
                            {models.map((model, index) => {
                              const value = row.getValue(model)
                              const formattedValue = row.format ? row.format(value) : (value?.toString() || 'N/A')
                              const isBest = bestIndex === index
                              
                              return (
                                <td key={model.id} className="py-3 px-4 text-center">
                                  <div className="flex items-center justify-center gap-2">
                                    <span className={cn(
                                      'font-medium',
                                      isBest && 'text-amber-600 dark:text-amber-400'
                                    )}>
                                      {formattedValue}
                                    </span>
                                    {getComparisonIcon(value, bestIndex, index)}
                                  </div>
                                </td>
                              )
                            })}
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            )}
          </Card>
        )
      })}
    </div>
  )
}
