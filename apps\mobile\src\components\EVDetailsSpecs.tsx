import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { EVModel } from '@greenmiles-ev/shared';

interface EVDetailsSpecsProps {
  model: EVModel;
}

interface SpecSection {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  specs: Array<{
    label: string;
    value: string | number | null;
    unit?: string;
  }>;
}

export function EVDetailsSpecs({ model }: EVDetailsSpecsProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(['performance']);

  const formatValue = (value: string | number | null, unit?: string) => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      return unit ? `${value.toLocaleString()} ${unit}` : value.toLocaleString();
    }
    return unit ? `${value} ${unit}` : value;
  };

  const toggleSection = (sectionTitle: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionTitle) 
        ? prev.filter(s => s !== sectionTitle)
        : [...prev, sectionTitle]
    );
  };

  const specSections: SpecSection[] = [
    {
      title: 'Performance',
      icon: 'flash',
      specs: [
        { label: '0-60 mph', value: model.acceleration_0_60_mph, unit: 'seconds' },
        { label: 'Top Speed', value: model.top_speed_mph, unit: 'mph' },
        { label: 'Motor Power', value: model.motor_power_hp, unit: 'hp' },
        { label: 'Motor Torque', value: model.motor_torque_lb_ft, unit: 'lb-ft' },
        { label: 'Drivetrain', value: model.drivetrain?.toUpperCase() },
      ],
    },
    {
      title: 'Battery & Range',
      icon: 'battery-charging',
      specs: [
        { label: 'Battery Capacity', value: model.battery_capacity_kwh, unit: 'kWh' },
        { label: 'EPA Range', value: model.range_epa_miles, unit: 'miles' },
        { label: 'WLTP Range', value: model.range_wltp_miles, unit: 'miles' },
        { label: 'Real-World Range', value: model.range_real_world_miles, unit: 'miles' },
        { label: 'Efficiency (EPA)', value: model.efficiency_mpge, unit: 'MPGe' },
      ],
    },
    {
      title: 'Charging',
      icon: 'flash-outline',
      specs: [
        { label: 'DC Fast Charging', value: model.charging_speed_dc_kw, unit: 'kW' },
        { label: 'AC Charging', value: model.charging_speed_ac_kw, unit: 'kW' },
        { label: '10-80% Charging Time', value: model.charging_time_10_80_minutes, unit: 'minutes' },
        { label: 'Charging Ports', value: model.charging_ports?.join(', ') },
      ],
    },
    {
      title: 'Dimensions',
      icon: 'resize',
      specs: [
        { label: 'Length', value: model.length_inches, unit: 'inches' },
        { label: 'Width', value: model.width_inches, unit: 'inches' },
        { label: 'Height', value: model.height_inches, unit: 'inches' },
        { label: 'Wheelbase', value: model.wheelbase_inches, unit: 'inches' },
        { label: 'Ground Clearance', value: model.ground_clearance_inches, unit: 'inches' },
        { label: 'Curb Weight', value: model.curb_weight_lbs, unit: 'lbs' },
      ],
    },
    {
      title: 'Interior & Cargo',
      icon: 'car',
      specs: [
        { label: 'Seating Capacity', value: model.seating_capacity, unit: 'seats' },
        { label: 'Cargo Volume', value: model.cargo_volume_cubic_ft, unit: 'cubic ft' },
        { label: 'Body Type', value: model.body_type?.charAt(0).toUpperCase() + model.body_type?.slice(1) },
      ],
    },
    {
      title: 'Pricing',
      icon: 'cash',
      specs: [
        { label: 'MSRP', value: model.price_msrp ? `$${(model.price_msrp / 100).toLocaleString()}` : null },
        { label: 'Base Price', value: model.price_base ? `$${(model.price_base / 100).toLocaleString()}` : null },
        { label: 'As Tested Price', value: model.price_as_tested ? `$${(model.price_as_tested / 100).toLocaleString()}` : null },
        { label: 'Production Status', value: model.production_status?.charAt(0).toUpperCase() + model.production_status?.slice(1) },
      ],
    },
  ];

  const SpecRow = ({ label, value, unit }: { label: string; value: string | number | null; unit?: string }) => (
    <View className="flex-row justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
      <Text className="text-gray-700 flex-1">{label}</Text>
      <Text className="font-medium text-gray-900 text-right">
        {formatValue(value, unit)}
      </Text>
    </View>
  );

  const SpecSection = ({ section }: { section: SpecSection }) => {
    const isExpanded = expandedSections.includes(section.title);
    const hasValidSpecs = section.specs.some(spec => spec.value !== null && spec.value !== undefined);

    if (!hasValidSpecs) return null;

    return (
      <View className="bg-white mx-4 mb-4 rounded-xl shadow-sm overflow-hidden">
        <TouchableOpacity
          onPress={() => toggleSection(section.title)}
          className="flex-row items-center justify-between p-4 bg-gray-50"
        >
          <View className="flex-row items-center">
            <Ionicons name={section.icon} size={20} color="#6b7280" />
            <Text className="text-lg font-semibold text-gray-900 ml-3">
              {section.title}
            </Text>
          </View>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>

        {isExpanded && (
          <View className="p-4">
            {section.specs
              .filter(spec => spec.value !== null && spec.value !== undefined)
              .map((spec, index) => (
                <SpecRow
                  key={index}
                  label={spec.label}
                  value={spec.value}
                  unit={spec.unit}
                />
              ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View className="bg-white mx-4 mt-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-2">
          Complete Specifications
        </Text>
        <Text className="text-gray-600">
          Detailed technical specifications for the {model.make} {model.model}
          {model.trim && ` ${model.trim}`}
        </Text>
      </View>

      {/* Specification Sections */}
      {specSections.map((section, index) => (
        <SpecSection key={index} section={section} />
      ))}

      {/* Features Section */}
      {model.features && Object.keys(model.features).length > 0 && (
        <View className="bg-white mx-4 mb-4 rounded-xl shadow-sm overflow-hidden">
          <TouchableOpacity
            onPress={() => toggleSection('Features')}
            className="flex-row items-center justify-between p-4 bg-gray-50"
          >
            <View className="flex-row items-center">
              <Ionicons name="star" size={20} color="#6b7280" />
              <Text className="text-lg font-semibold text-gray-900 ml-3">
                Features
              </Text>
            </View>
            <Ionicons
              name={expandedSections.includes('Features') ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="#6b7280"
            />
          </TouchableOpacity>

          {expandedSections.includes('Features') && (
            <View className="p-4">
              {Object.entries(model.features).map(([category, features], index) => (
                <View key={index} className="mb-4 last:mb-0">
                  <Text className="font-medium text-gray-900 mb-2 capitalize">
                    {category.replace(/_/g, ' ')}
                  </Text>
                  {Array.isArray(features) ? (
                    features.map((feature: string, featureIndex: number) => (
                      <View key={featureIndex} className="flex-row items-center mb-1">
                        <Ionicons name="checkmark" size={14} color="#22c55e" />
                        <Text className="text-gray-700 ml-2">{feature}</Text>
                      </View>
                    ))
                  ) : (
                    <Text className="text-gray-700">{String(features)}</Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      )}

      {/* Safety Ratings */}
      {model.safety_ratings && Object.keys(model.safety_ratings).length > 0 && (
        <View className="bg-white mx-4 mb-4 rounded-xl shadow-sm overflow-hidden">
          <TouchableOpacity
            onPress={() => toggleSection('Safety')}
            className="flex-row items-center justify-between p-4 bg-gray-50"
          >
            <View className="flex-row items-center">
              <Ionicons name="shield-checkmark" size={20} color="#6b7280" />
              <Text className="text-lg font-semibold text-gray-900 ml-3">
                Safety Ratings
              </Text>
            </View>
            <Ionicons
              name={expandedSections.includes('Safety') ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="#6b7280"
            />
          </TouchableOpacity>

          {expandedSections.includes('Safety') && (
            <View className="p-4">
              {Object.entries(model.safety_ratings).map(([rating, value], index) => (
                <SpecRow
                  key={index}
                  label={rating.replace(/_/g, ' ').toUpperCase()}
                  value={value}
                />
              ))}
            </View>
          )}
        </View>
      )}

      {/* Warranty Information */}
      {model.warranty_info && Object.keys(model.warranty_info).length > 0 && (
        <View className="bg-white mx-4 mb-4 rounded-xl shadow-sm overflow-hidden">
          <TouchableOpacity
            onPress={() => toggleSection('Warranty')}
            className="flex-row items-center justify-between p-4 bg-gray-50"
          >
            <View className="flex-row items-center">
              <Ionicons name="document-text" size={20} color="#6b7280" />
              <Text className="text-lg font-semibold text-gray-900 ml-3">
                Warranty
              </Text>
            </View>
            <Ionicons
              name={expandedSections.includes('Warranty') ? 'chevron-up' : 'chevron-down'}
              size={20}
              color="#6b7280"
            />
          </TouchableOpacity>

          {expandedSections.includes('Warranty') && (
            <View className="p-4">
              {Object.entries(model.warranty_info).map(([type, value], index) => (
                <SpecRow
                  key={index}
                  label={type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  value={value}
                />
              ))}
            </View>
          )}
        </View>
      )}

      {/* Bottom Padding for Floating Buttons */}
      <View className="h-20" />
    </ScrollView>
  );
}
