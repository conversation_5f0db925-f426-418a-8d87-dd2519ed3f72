import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  FlatList,
  Dimensions,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { router, useLocalSearchParams } from 'expo-router'
import { EVModel } from '@greenmiles-ev/shared'
import { evModelsApi } from '../../src/lib/supabase'
import { useComparison } from '../../src/contexts/ComparisonContext'
import { EVDetailsOverview } from '../../src/components/EVDetailsOverview'
import { EVDetailsSpecs } from '../../src/components/EVDetailsSpecs'
import { EVDetailsCostAnalysis } from '../../src/components/EVDetailsCostAnalysis'
import { EVDetailsRealWorld } from '../../src/components/EVDetailsRealWorld'
import { SimilarVehicles } from '../../src/components/SimilarVehicles'

const { width: screenWidth } = Dimensions.get('window')

type TabType = 'overview' | 'specs' | 'costs' | 'real-world'

interface Tab {
  id: TabType
  label: string
  icon: keyof typeof Ionicons.glyphMap
}

const tabs: Tab[] = [
  { id: 'overview', label: 'Overview', icon: 'information-circle' },
  { id: 'specs', label: 'Specs', icon: 'list' },
  { id: 'costs', label: 'Costs', icon: 'calculator' },
  { id: 'real-world', label: 'Real-World', icon: 'speedometer' },
]

export default function EVModelDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>()
  const [model, setModel] = useState<EVModel | null>(null)
  const [similarModels, setSimilarModels] = useState<EVModel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [imageIndex, setImageIndex] = useState(0)

  const { addToComparison, removeFromComparison, isInComparison } = useComparison()

  useEffect(() => {
    if (id) {
      fetchModelDetails()
    }
  }, [id])

  const fetchModelDetails = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error: fetchError } = await evModelsApi.getById(id)

      if (fetchError) {
        throw fetchError
      }

      if (data) {
        // Transform data to include manufacturer info
        const transformedData = {
          ...data,
          manufacturer: data.ev_manufacturers,
        }
        setModel(transformedData)

        // Fetch similar models
        fetchSimilarModels(transformedData)
      }
    } catch (err) {
      console.error('Error fetching model details:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch model details')
    } finally {
      setLoading(false)
    }
  }

  const fetchSimilarModels = async (currentModel: any) => {
    try {
      const { data } = await evModelsApi.getAll({
        bodyType: currentModel.body_type,
        priceMin: Math.max(0, (currentModel.price_msrp || 0) - 1000000).toString(), // $10k range
        priceMax: ((currentModel.price_msrp || 0) + 1000000).toString(),
      })

      if (data) {
        const similar = data.filter((m: any) => m.id !== currentModel.id).slice(0, 4)
        setSimilarModels(similar)
      }
    } catch (err) {
      console.error('Error fetching similar models:', err)
    }
  }

  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA'
    return `$${(priceInCents / 100).toLocaleString()}`
  }

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A'
    return `${range} mi`
  }

  const getImages = () => {
    if (model?.images && model.images.length > 0) {
      return model.images.map((url) => ({ uri: url }))
    }
    return []
  }

  const getCurrentImage = () => {
    const images = getImages()
    if (images.length > 0) {
      return images[imageIndex] || images[0]
    }
    return null
  }

  const handleCompareToggle = () => {
    if (!model) return

    if (isInComparison(model.id)) {
      removeFromComparison(model.id)
    } else {
      const success = addToComparison(model)
      if (!success) {
        Alert.alert(
          'Comparison Limit',
          'You can compare up to 4 vehicles at once. Remove one to add another.',
          [{ text: 'OK' }]
        )
      }
    }
  }

  if (loading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <Text className="text-gray-600">Loading vehicle details...</Text>
      </View>
    )
  }

  if (error || !model) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <Ionicons name="alert-circle" size={48} color="#ef4444" />
        <Text className="text-lg font-semibold text-gray-900 mt-4 mb-2">Something went wrong</Text>
        <Text className="text-gray-600 text-center px-8 mb-6">{error || 'Vehicle not found'}</Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="bg-electric-600 rounded-lg px-6 py-3"
        >
          <Text className="text-white font-semibold">Go Back</Text>
        </TouchableOpacity>
      </View>
    )
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <EVDetailsOverview model={model} similarModels={similarModels} />
      case 'specs':
        return <EVDetailsSpecs model={model} />
      case 'costs':
        return <EVDetailsCostAnalysis model={model} />
      case 'real-world':
        return <EVDetailsRealWorld model={model} />
      default:
        return <EVDetailsOverview model={model} similarModels={similarModels} />
    }
  }

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 pt-12 bg-electric-600">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-white" numberOfLines={1}>
          {model.make} {model.model}
        </Text>
        <View className="flex-row items-center gap-3">
          <TouchableOpacity onPress={handleCompareToggle}>
            <Ionicons
              name={isInComparison(model.id) ? 'analytics' : 'analytics-outline'}
              size={24}
              color="white"
            />
          </TouchableOpacity>
          <TouchableOpacity>
            <Ionicons name="share-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Image Gallery */}
      <View className="relative">
        {getCurrentImage() ? (
          <FlatList
            data={getImages()}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const newIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth)
              setImageIndex(newIndex)
            }}
            renderItem={({ item }) => (
              <Image source={item} style={{ width: screenWidth, height: 280 }} resizeMode="cover" />
            )}
            keyExtractor={(_, index) => index.toString()}
          />
        ) : (
          <View className="w-full h-72 bg-gray-200 items-center justify-center">
            <Ionicons name="car" size={80} color="#9ca3af" />
          </View>
        )}

        {/* Image Indicators */}
        {getImages().length > 1 && (
          <View className="absolute bottom-4 left-0 right-0 flex-row justify-center gap-2">
            {getImages().map((_, index) => (
              <View
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === imageIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </View>
        )}

        {/* Badges */}
        <View className="absolute top-4 left-4 flex-row gap-2">
          {model.is_featured && (
            <View className="bg-electric-600 rounded-full px-3 py-1">
              <Text className="text-white text-xs font-medium">Featured</Text>
            </View>
          )}
          {model.editor_choice && (
            <View className="bg-blue-600 rounded-full px-3 py-1">
              <Text className="text-white text-xs font-medium">Editor's Choice</Text>
            </View>
          )}
          {model.best_value && (
            <View className="bg-green-600 rounded-full px-3 py-1">
              <Text className="text-white text-xs font-medium">Best Value</Text>
            </View>
          )}
        </View>
      </View>

      {/* Basic Info */}
      <View className="p-4 bg-white">
        <Text className="text-2xl font-bold text-gray-900">
          {model.make} {model.model}
        </Text>
        {model.trim && <Text className="text-lg text-gray-600 mt-1">{model.trim}</Text>}
        <Text className="text-2xl font-bold text-electric-600 mt-2">
          {formatPrice(model.price_msrp)}
        </Text>

        {model.manufacturer && (
          <Text className="text-sm text-gray-500 mt-1">by {model.manufacturer.name}</Text>
        )}
      </View>

      {/* Tab Navigation */}
      <View className="bg-white border-b border-gray-200">
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-4">
          <View className="flex-row gap-1">
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                onPress={() => setActiveTab(tab.id)}
                className={`px-4 py-3 rounded-lg mr-2 ${
                  activeTab === tab.id ? 'bg-electric-600' : 'bg-gray-100'
                }`}
              >
                <View className="flex-row items-center">
                  <Ionicons
                    name={tab.icon}
                    size={16}
                    color={activeTab === tab.id ? 'white' : '#6b7280'}
                  />
                  <Text
                    className={`ml-2 font-medium ${
                      activeTab === tab.id ? 'text-white' : 'text-gray-700'
                    }`}
                  >
                    {tab.label}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Tab Content */}
      <ScrollView className="flex-1 bg-gray-50">{renderTabContent()}</ScrollView>

      {/* Floating Action Buttons */}
      <View className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <View className="flex-row gap-3">
          <TouchableOpacity
            onPress={handleCompareToggle}
            className={`flex-1 rounded-xl py-4 items-center ${
              isInComparison(model.id)
                ? 'bg-gray-100 border border-electric-600'
                : 'bg-electric-600'
            }`}
          >
            <Text
              className={`font-semibold ${
                isInComparison(model.id) ? 'text-electric-600' : 'text-white'
              }`}
            >
              {isInComparison(model.id) ? 'Remove from Comparison' : 'Add to Comparison'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => router.push('/comparison')}
            className="px-6 py-4 bg-gray-100 rounded-xl items-center justify-center"
          >
            <Ionicons name="analytics" size={20} color="#374151" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}
