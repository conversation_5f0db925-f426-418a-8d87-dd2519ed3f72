import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { EVModel } from '@greenmiles-ev/shared';

interface DecisionMatrixProps {
  models: EVModel[];
}

interface DecisionWeights {
  price: number;
  range: number;
  efficiency: number;
  performance: number;
  charging: number;
}

interface ModelScore {
  model: EVModel;
  scores: {
    price: number;
    range: number;
    efficiency: number;
    performance: number;
    charging: number;
  };
  weightedTotal: number;
  rank: number;
}

const PRESET_PROFILES = {
  commuter: { price: 30, range: 25, efficiency: 30, performance: 5, charging: 10 },
  family: { price: 25, range: 30, efficiency: 20, performance: 10, charging: 15 },
  performance: { price: 15, range: 20, efficiency: 10, performance: 40, charging: 15 },
  budget: { price: 50, range: 20, efficiency: 20, performance: 5, charging: 5 },
  balanced: { price: 20, range: 20, efficiency: 20, performance: 20, charging: 20 },
};

export function DecisionMatrix({ models }: DecisionMatrixProps) {
  const [weights, setWeights] = useState<DecisionWeights>(PRESET_PROFILES.balanced);
  const [selectedProfile, setSelectedProfile] = useState<string>('balanced');

  const normalizeScore = (value: number | null, min: number, max: number, reverse = false): number => {
    if (value === null || value === undefined) return 0;
    if (max === min) return 50; // If all values are the same, give middle score
    
    const normalized = ((value - min) / (max - min)) * 100;
    return reverse ? 100 - normalized : normalized;
  };

  const calculateScores = (): ModelScore[] => {
    if (models.length === 0) return [];

    // Get min/max values for normalization
    const prices = models.map(m => m.price_msrp).filter(p => p !== null) as number[];
    const ranges = models.map(m => m.range_epa_miles).filter(r => r !== null) as number[];
    const efficiencies = models.map(m => m.efficiency_mpge).filter(e => e !== null) as number[];
    const accelerations = models.map(m => m.acceleration_0_60_mph).filter(a => a !== null) as number[];
    const chargingSpeeds = models.map(m => m.charging_speed_dc_kw).filter(c => c !== null) as number[];

    const priceRange = { min: Math.min(...prices), max: Math.max(...prices) };
    const rangeRange = { min: Math.min(...ranges), max: Math.max(...ranges) };
    const efficiencyRange = { min: Math.min(...efficiencies), max: Math.max(...efficiencies) };
    const accelerationRange = { min: Math.min(...accelerations), max: Math.max(...accelerations) };
    const chargingRange = { min: Math.min(...chargingSpeeds), max: Math.max(...chargingSpeeds) };

    const modelScores = models.map((model): ModelScore => {
      const scores = {
        price: normalizeScore(model.price_msrp, priceRange.min, priceRange.max, true), // Lower price is better
        range: normalizeScore(model.range_epa_miles, rangeRange.min, rangeRange.max),
        efficiency: normalizeScore(model.efficiency_mpge, efficiencyRange.min, efficiencyRange.max),
        performance: normalizeScore(model.acceleration_0_60_mph, accelerationRange.min, accelerationRange.max, true), // Lower 0-60 is better
        charging: normalizeScore(model.charging_speed_dc_kw, chargingRange.min, chargingRange.max),
      };

      const weightedTotal = (
        (scores.price * weights.price) +
        (scores.range * weights.range) +
        (scores.efficiency * weights.efficiency) +
        (scores.performance * weights.performance) +
        (scores.charging * weights.charging)
      ) / 100;

      return {
        model,
        scores,
        weightedTotal,
        rank: 0, // Will be set after sorting
      };
    });

    // Sort by weighted total and assign ranks
    modelScores.sort((a, b) => b.weightedTotal - a.weightedTotal);
    modelScores.forEach((score, index) => {
      score.rank = index + 1;
    });

    return modelScores;
  };

  const updateWeight = (category: keyof DecisionWeights, value: number) => {
    setWeights(prev => ({ ...prev, [category]: value }));
    setSelectedProfile('custom');
  };

  const applyProfile = (profileName: string) => {
    if (profileName in PRESET_PROFILES) {
      setWeights(PRESET_PROFILES[profileName as keyof typeof PRESET_PROFILES]);
      setSelectedProfile(profileName);
    }
  };

  const modelScores = calculateScores();
  const totalWeights = Object.values(weights).reduce((sum, weight) => sum + weight, 0);

  const ScoreBar = ({ score, color = 'bg-blue-500' }: { score: number; color?: string }) => (
    <View className="bg-gray-200 rounded-full h-2 flex-1">
      <View
        className={`h-2 rounded-full ${color}`}
        style={{ width: `${score}%` }}
      />
    </View>
  );

  const WeightSlider = ({ 
    label, 
    category, 
    icon 
  }: { 
    label: string; 
    category: keyof DecisionWeights; 
    icon: keyof typeof Ionicons.glyphMap;
  }) => (
    <View className="mb-4">
      <View className="flex-row items-center justify-between mb-2">
        <View className="flex-row items-center">
          <Ionicons name={icon} size={16} color="#6b7280" />
          <Text className="text-gray-700 ml-2">{label}</Text>
        </View>
        <Text className="font-medium text-gray-900">{weights[category]}%</Text>
      </View>
      <Slider
        style={{ width: '100%', height: 40 }}
        minimumValue={0}
        maximumValue={50}
        step={5}
        value={weights[category]}
        onValueChange={(value) => updateWeight(category, value)}
        minimumTrackTintColor="#22c55e"
        maximumTrackTintColor="#d1d5db"
        thumbStyle={{ backgroundColor: '#22c55e' }}
      />
    </View>
  );

  const ModelScoreCard = ({ modelScore }: { modelScore: ModelScore }) => {
    const { model, scores, weightedTotal, rank } = modelScore;
    const isTopChoice = rank === 1;

    return (
      <View className={`bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm ${
        isTopChoice ? 'border-2 border-green-200' : ''
      }`}>
        <View className="flex-row items-center justify-between mb-4">
          <View className="flex-row items-center">
            <Text className="text-lg font-bold text-gray-400 mr-3">#{rank}</Text>
            <View>
              <Text className="text-lg font-semibold text-gray-900">
                {model.make} {model.model}
              </Text>
              {model.trim && (
                <Text className="text-sm text-gray-600">{model.trim}</Text>
              )}
            </View>
          </View>
          
          {isTopChoice && (
            <View className="bg-green-100 rounded-full px-3 py-1">
              <Text className="text-green-700 text-sm font-medium">Top Choice</Text>
            </View>
          )}
        </View>

        {/* Overall Score */}
        <View className="mb-4">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="font-medium text-gray-900">Overall Score</Text>
            <Text className={`text-lg font-bold ${isTopChoice ? 'text-green-600' : 'text-gray-900'}`}>
              {Math.round(weightedTotal)}/100
            </Text>
          </View>
          <ScoreBar 
            score={weightedTotal} 
            color={isTopChoice ? 'bg-green-500' : 'bg-blue-500'} 
          />
        </View>

        {/* Category Scores */}
        <View className="space-y-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <Ionicons name="cash" size={14} color="#6b7280" />
              <Text className="text-sm text-gray-600 ml-2">Price</Text>
            </View>
            <View className="flex-row items-center flex-1">
              <ScoreBar score={scores.price} color="bg-green-400" />
              <Text className="text-sm font-medium text-gray-900 ml-2 w-8">
                {Math.round(scores.price)}
              </Text>
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <Ionicons name="speedometer" size={14} color="#6b7280" />
              <Text className="text-sm text-gray-600 ml-2">Range</Text>
            </View>
            <View className="flex-row items-center flex-1">
              <ScoreBar score={scores.range} color="bg-blue-400" />
              <Text className="text-sm font-medium text-gray-900 ml-2 w-8">
                {Math.round(scores.range)}
              </Text>
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <Ionicons name="leaf" size={14} color="#6b7280" />
              <Text className="text-sm text-gray-600 ml-2">Efficiency</Text>
            </View>
            <View className="flex-row items-center flex-1">
              <ScoreBar score={scores.efficiency} color="bg-green-400" />
              <Text className="text-sm font-medium text-gray-900 ml-2 w-8">
                {Math.round(scores.efficiency)}
              </Text>
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <Ionicons name="flash" size={14} color="#6b7280" />
              <Text className="text-sm text-gray-600 ml-2">Performance</Text>
            </View>
            <View className="flex-row items-center flex-1">
              <ScoreBar score={scores.performance} color="bg-purple-400" />
              <Text className="text-sm font-medium text-gray-900 ml-2 w-8">
                {Math.round(scores.performance)}
              </Text>
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <Ionicons name="flash-outline" size={14} color="#6b7280" />
              <Text className="text-sm text-gray-600 ml-2">Charging</Text>
            </View>
            <View className="flex-row items-center flex-1">
              <ScoreBar score={scores.charging} color="bg-yellow-400" />
              <Text className="text-sm font-medium text-gray-900 ml-2 w-8">
                {Math.round(scores.charging)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Decision Profiles */}
      <View className="bg-white m-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Decision Profiles</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row gap-2">
            {Object.entries(PRESET_PROFILES).map(([key, profile]) => (
              <TouchableOpacity
                key={key}
                onPress={() => applyProfile(key)}
                className={`px-4 py-2 rounded-lg ${
                  selectedProfile === key ? 'bg-electric-600' : 'bg-gray-100'
                }`}
              >
                <Text className={`font-medium capitalize ${
                  selectedProfile === key ? 'text-white' : 'text-gray-700'
                }`}>
                  {key}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Priority Weights */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-lg font-semibold text-gray-900">Priority Weights</Text>
          <Text className="text-sm text-gray-600">Total: {totalWeights}%</Text>
        </View>
        
        <WeightSlider label="Price" category="price" icon="cash" />
        <WeightSlider label="Range" category="range" icon="speedometer" />
        <WeightSlider label="Efficiency" category="efficiency" icon="leaf" />
        <WeightSlider label="Performance" category="performance" icon="flash" />
        <WeightSlider label="Charging Speed" category="charging" icon="flash-outline" />
        
        {totalWeights !== 100 && (
          <View className="bg-yellow-50 rounded-lg p-3 mt-2">
            <View className="flex-row items-center">
              <Ionicons name="warning" size={16} color="#f59e0b" />
              <Text className="text-yellow-800 text-sm ml-2">
                Weights should total 100% for accurate scoring
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Model Rankings */}
      <View className="pb-20">
        <View className="px-4 mb-4">
          <Text className="text-lg font-semibold text-gray-900">Decision Matrix Results</Text>
          <Text className="text-gray-600 text-sm">
            Vehicles ranked by weighted score based on your priorities
          </Text>
        </View>
        
        {modelScores.map((modelScore) => (
          <ModelScoreCard key={modelScore.model.id} modelScore={modelScore} />
        ))}
      </View>
    </ScrollView>
  );
}
