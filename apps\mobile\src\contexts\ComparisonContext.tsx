import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { EVModel } from '@greenmiles-ev/shared';

interface ComparisonContextType {
  comparisonList: EVModel[];
  comparisonCount: number;
  maxComparisons: number;
  addToComparison: (model: EVModel) => boolean;
  removeFromComparison: (modelId: string) => void;
  clearComparison: () => void;
  isInComparison: (modelId: string) => boolean;
  canAddMore: boolean;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

const STORAGE_KEY = 'ev_comparison_list';
const MAX_COMPARISONS = 4;

interface ComparisonProviderProps {
  children: ReactNode;
}

export function ComparisonProvider({ children }: ComparisonProviderProps) {
  const [comparisonList, setComparisonList] = useState<EVModel[]>([]);

  // Load comparison list from storage on mount
  useEffect(() => {
    loadComparisonList();
  }, []);

  // Save to storage whenever comparison list changes
  useEffect(() => {
    saveComparisonList();
  }, [comparisonList]);

  const loadComparisonList = async () => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setComparisonList(parsed);
      }
    } catch (error) {
      console.error('Error loading comparison list:', error);
    }
  };

  const saveComparisonList = async () => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(comparisonList));
    } catch (error) {
      console.error('Error saving comparison list:', error);
    }
  };

  const addToComparison = (model: EVModel): boolean => {
    // Check if already in comparison
    if (comparisonList.some(item => item.id === model.id)) {
      return false;
    }

    // Check if at max capacity
    if (comparisonList.length >= MAX_COMPARISONS) {
      return false;
    }

    setComparisonList(prev => [...prev, model]);
    return true;
  };

  const removeFromComparison = (modelId: string) => {
    setComparisonList(prev => prev.filter(item => item.id !== modelId));
  };

  const clearComparison = () => {
    setComparisonList([]);
  };

  const isInComparison = (modelId: string): boolean => {
    return comparisonList.some(item => item.id === modelId);
  };

  const canAddMore = comparisonList.length < MAX_COMPARISONS;
  const comparisonCount = comparisonList.length;

  const value: ComparisonContextType = {
    comparisonList,
    comparisonCount,
    maxComparisons: MAX_COMPARISONS,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    canAddMore,
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
}

export function useComparison(): ComparisonContextType {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
}
