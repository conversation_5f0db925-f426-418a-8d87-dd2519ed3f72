import type {
  EVModel,
  BuyerMetrics,
  ComparisonMetrics,
  UserPriorities,
  PriorityWeights,
  EVBodyType,
  ProductionStatus,
  UseCase,
} from "../types";

// ===== FORMATTING UTILITIES =====

/**
 * Format price in cents to display format
 */
export function formatPrice(priceInCents: number | null | undefined): string {
  if (!priceInCents || priceInCents <= 0) return "Price not available";

  const price = priceInCents / 100;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}

/**
 * Format range with units
 */
export function formatRange(miles: number | null | undefined): string {
  if (!miles || miles <= 0) return "Range not available";
  return `${miles} miles`;
}

/**
 * Format efficiency (MPGe)
 */
export function formatEfficiency(mpge: number | null | undefined): string {
  if (!mpge || mpge <= 0) return "Efficiency not available";
  return `${mpge} MPGe`;
}

/**
 * Format acceleration time
 */
export function formatAcceleration(seconds: number | null | undefined): string {
  if (!seconds || seconds <= 0) return "Acceleration not available";
  return `${seconds}s (0-60 mph)`;
}

/**
 * Format charging time
 */
export function formatChargingTime(minutes: number | null | undefined): string {
  if (!minutes || minutes <= 0) return "Charging time not available";

  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m (10-80%)`
      : `${hours}h (10-80%)`;
  }

  return `${minutes}m (10-80%)`;
}

/**
 * Format power with units
 */
export function formatPower(hp: number | null | undefined): string {
  if (!hp || hp <= 0) return "Power not available";
  return `${hp} hp`;
}

/**
 * Format battery capacity
 */
export function formatBatteryCapacity(kwh: number | null | undefined): string {
  if (!kwh || kwh <= 0) return "Battery capacity not available";
  return `${kwh} kWh`;
}

/**
 * Format charging speed
 */
export function formatChargingSpeed(kw: number | null | undefined): string {
  if (!kw || kw <= 0) return "Charging speed not available";
  return `${kw} kW`;
}

// ===== CALCULATION UTILITIES =====

/**
 * Calculate buyer-focused metrics for an EV model
 */
export function calculateBuyerMetrics(evModel: EVModel): BuyerMetrics {
  const costPerMile =
    evModel.price_msrp && evModel.range_epa_miles && evModel.range_epa_miles > 0
      ? Math.round(evModel.price_msrp / 100 / evModel.range_epa_miles)
      : null;

  const chargingEfficiency =
    evModel.charging_speed_dc_kw &&
    evModel.battery_capacity_kwh &&
    evModel.battery_capacity_kwh > 0
      ? Math.round(
          (evModel.charging_speed_dc_kw / evModel.battery_capacity_kwh) * 100
        ) / 100
      : null;

  const practicalRange =
    evModel.range_real_world_miles ||
    (evModel.range_epa_miles
      ? Math.round(evModel.range_epa_miles * 0.85)
      : null);

  const powerToWeightRatio =
    evModel.motor_power_hp &&
    evModel.curb_weight_lbs &&
    evModel.curb_weight_lbs > 0
      ? Math.round((evModel.motor_power_hp / evModel.curb_weight_lbs) * 1000) /
        1000
      : null;

  return {
    costPerMile,
    chargingEfficiency,
    practicalRange,
    powerToWeightRatio,
  };
}

/**
 * Calculate comparison metrics for multiple EV models
 */
export function calculateComparisonMetrics(
  evModels: EVModel[]
): ComparisonMetrics {
  const prices = evModels.map((m) => m.price_msrp || 0).filter((p) => p > 0);
  const ranges = evModels
    .map((m) => m.range_epa_miles || 0)
    .filter((r) => r > 0);
  const chargingSpeeds = evModels
    .map((m) => m.charging_speed_dc_kw || 0)
    .filter((s) => s > 0);
  const accelerations = evModels
    .map((m) => m.acceleration_0_60_mph || 999)
    .filter((a) => a < 999);

  return {
    priceRange: {
      min: prices.length > 0 ? Math.min(...prices) : 0,
      max: prices.length > 0 ? Math.max(...prices) : 0,
    },
    rangeComparison: {
      min: ranges.length > 0 ? Math.min(...ranges) : 0,
      max: ranges.length > 0 ? Math.max(...ranges) : 0,
    },
    chargingSpeed: {
      min: chargingSpeeds.length > 0 ? Math.min(...chargingSpeeds) : 0,
      max: chargingSpeeds.length > 0 ? Math.max(...chargingSpeeds) : 0,
    },
    acceleration: {
      fastest: accelerations.length > 0 ? Math.min(...accelerations) : 0,
      slowest: accelerations.length > 0 ? Math.max(...accelerations) : 0,
    },
  };
}

/**
 * Calculate match score for an EV model based on user preferences
 */
export function calculateMatchScore(
  evModel: EVModel,
  preferences: {
    budget_min?: number | null;
    budget_max?: number | null;
    range_requirement_miles?: number | null;
    body_type_preferences?: string[] | null;
    priority_weights?: PriorityWeights | null;
  }
): number {
  let score = 0;

  // Budget match (30 points max)
  if (evModel.price_msrp) {
    const budgetMin = preferences.budget_min || 0;
    const budgetMax = preferences.budget_max || 999999999;
    if (evModel.price_msrp >= budgetMin && evModel.price_msrp <= budgetMax) {
      score += 30;
    }
  }

  // Range requirement (25 points max)
  if (evModel.range_epa_miles && preferences.range_requirement_miles) {
    if (evModel.range_epa_miles >= preferences.range_requirement_miles) {
      score += 25;
    }
  }

  // Body type preference (20 points max)
  if (evModel.body_type && preferences.body_type_preferences) {
    if (preferences.body_type_preferences.includes(evModel.body_type)) {
      score += 20;
    }
  }

  // Featured model bonus (10 points)
  if (evModel.is_featured) {
    score += 10;
  }

  // Best value bonus (10 points)
  if (evModel.best_value) {
    score += 10;
  }

  // Popularity score (up to 10 points)
  score += Math.min(evModel.popularity_score / 10, 10);

  return Math.round(score);
}

/**
 * Generate URL-friendly slug from EV model
 */
export function generateSlug(
  evModel: Pick<EVModel, "make" | "model" | "year" | "trim">
): string {
  const parts = [
    evModel.make,
    evModel.model,
    evModel.year.toString(),
    evModel.trim,
  ].filter(Boolean);

  return parts
    .join("-")
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
}

/**
 * Parse slug back to search parameters
 */
export function parseSlug(slug: string): {
  make?: string;
  model?: string;
  year?: number;
  trim?: string;
} {
  const parts = slug.split("-");

  if (parts.length < 3) return {};

  const year = parseInt(parts[parts.length - 2]);
  if (isNaN(year)) return {};

  return {
    make: parts[0],
    model: parts[1],
    year,
    trim: parts.length > 3 ? parts.slice(2, -1).join("-") : undefined,
  };
}

// ===== COMPARISON UTILITIES =====

/**
 * Compare two values and return comparison result
 */
export function compareValues(
  value1: number | null | undefined,
  value2: number | null | undefined,
  higherIsBetter: boolean = true
): "better" | "worse" | "equal" | "unknown" {
  if (value1 == null || value2 == null) return "unknown";

  if (value1 === value2) return "equal";

  if (higherIsBetter) {
    return value1 > value2 ? "better" : "worse";
  } else {
    return value1 < value2 ? "better" : "worse";
  }
}

/**
 * Get the best value in a comparison for a specific metric
 */
export function getBestInComparison(
  evModels: EVModel[],
  metric: keyof EVModel,
  higherIsBetter: boolean = true
): { model: EVModel; value: any } | null {
  const modelsWithValue = evModels
    .map((model) => ({ model, value: model[metric] }))
    .filter((item) => item.value != null && item.value !== 0);

  if (modelsWithValue.length === 0) return null;

  const best = modelsWithValue.reduce((best, current) => {
    if (higherIsBetter) {
      return (current.value || 0) > (best.value || 0) ? current : best;
    } else {
      return (current.value || 0) < (best.value || 0) ? current : best;
    }
  });

  return best;
}

// ===== VALIDATION UTILITIES =====

/**
 * Validate EV model data completeness
 */
export function validateEVModelData(evModel: Partial<EVModel>): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const requiredFields = ["make", "model", "year", "battery_capacity_kwh"];
  const importantFields = [
    "price_msrp",
    "range_epa_miles",
    "charging_speed_dc_kw",
  ];

  const missingFields = requiredFields.filter(
    (field) => !evModel[field as keyof EVModel]
  );
  const missingImportant = importantFields.filter(
    (field) => !evModel[field as keyof EVModel]
  );

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings: missingImportant.map(
      (field) => `Missing important field: ${field}`
    ),
  };
}

/**
 * Check if EV model matches search criteria
 */
export function matchesSearchCriteria(
  evModel: EVModel,
  criteria: {
    query?: string;
    make?: string;
    bodyType?: EVBodyType;
    priceRange?: { min: number; max: number };
    rangeMin?: number;
    productionStatus?: ProductionStatus;
  }
): boolean {
  // Text search
  if (criteria.query) {
    const searchText =
      `${evModel.make} ${evModel.model} ${evModel.trim || ""}`.toLowerCase();
    if (!searchText.includes(criteria.query.toLowerCase())) {
      return false;
    }
  }

  // Make filter
  if (
    criteria.make &&
    evModel.make.toLowerCase() !== criteria.make.toLowerCase()
  ) {
    return false;
  }

  // Body type filter
  if (criteria.bodyType && evModel.body_type !== criteria.bodyType) {
    return false;
  }

  // Price range filter
  if (criteria.priceRange && evModel.price_msrp) {
    if (
      evModel.price_msrp < criteria.priceRange.min ||
      evModel.price_msrp > criteria.priceRange.max
    ) {
      return false;
    }
  }

  // Range filter
  if (criteria.rangeMin && evModel.range_epa_miles) {
    if (evModel.range_epa_miles < criteria.rangeMin) {
      return false;
    }
  }

  // Production status filter
  if (
    criteria.productionStatus &&
    evModel.production_status !== criteria.productionStatus
  ) {
    return false;
  }

  return true;
}
