# Dependencies
node_modules/

# Build outputs
build/
dist/
.docusaurus/
.cache/

# Generated files
.docusaurus/
.cache/
build/

# Environment variables
.env
.env.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar
