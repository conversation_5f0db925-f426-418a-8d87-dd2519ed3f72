'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Plus, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useComparisonActions } from '@/hooks/useComparisonActions'
import type { EVModel } from '@/shared/types'

interface ComparisonButtonProps {
  model: EVModel
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  showText?: boolean
  onClick?: (e: React.MouseEvent) => void
}

export function ComparisonButton({
  model,
  variant = 'outline',
  size = 'default',
  className,
  showText = true,
  onClick
}: ComparisonButtonProps) {
  const {
    toggleComparison,
    isInComparison,
    canAddMore,
    comparisonCount
  } = useComparisonActions()

  const isInCompare = isInComparison(model.id)
  const isDisabled = !isInCompare && !canAddMore

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isDisabled) {
      toggleComparison(model)
    }
    
    onClick?.(e)
  }

  const getButtonContent = () => {
    if (isInCompare) {
      return (
        <>
          <Check className="h-4 w-4" />
          {showText && <span>Added</span>}
        </>
      )
    }

    if (isDisabled) {
      return (
        <>
          <X className="h-4 w-4" />
          {showText && <span>Full</span>}
        </>
      )
    }

    return (
      <>
        <Plus className="h-4 w-4" />
        {showText && <span>Compare</span>}
      </>
    )
  }

  const getButtonVariant = () => {
    if (isInCompare) {
      return 'default'
    }
    return variant
  }

  const getButtonClassName = () => {
    return cn(
      'transition-colors',
      isInCompare && 'bg-electric-600 text-white hover:bg-electric-700',
      isDisabled && 'opacity-50 cursor-not-allowed',
      className
    )
  }

  return (
    <Button
      variant={getButtonVariant()}
      size={size}
      onClick={handleClick}
      disabled={isDisabled}
      className={getButtonClassName()}
      title={
        isInCompare
          ? 'Remove from comparison'
          : isDisabled
          ? `Maximum ${comparisonCount} models in comparison`
          : 'Add to comparison'
      }
    >
      {getButtonContent()}
    </Button>
  )
}
