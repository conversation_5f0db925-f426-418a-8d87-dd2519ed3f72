import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { EVModel } from '@greenmiles-ev/shared';

interface ComparisonRecommendationsProps {
  models: EVModel[];
}

export function ComparisonRecommendations({ models }: ComparisonRecommendationsProps) {
  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA';
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A';
    return `${range} mi`;
  };

  const generateInsights = () => {
    if (models.length === 0) return [];

    const insights = [];

    // Price analysis
    const prices = models.map(m => m.price_msrp).filter(p => p !== null) as number[];
    if (prices.length > 1) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const priceDiff = maxPrice - minPrice;
      const cheapestModel = models.find(m => m.price_msrp === minPrice);
      const mostExpensiveModel = models.find(m => m.price_msrp === maxPrice);

      insights.push({
        type: 'price',
        icon: 'cash' as keyof typeof Ionicons.glyphMap,
        title: 'Price Range Analysis',
        description: `Price difference of ${formatPrice(priceDiff)} between ${cheapestModel?.make} ${cheapestModel?.model} and ${mostExpensiveModel?.make} ${mostExpensiveModel?.model}`,
        recommendation: priceDiff > 2000000 ? 'Consider if the premium features justify the higher cost' : 'Prices are relatively close - focus on features and performance',
        color: 'bg-green-50 border-green-200 text-green-800',
      });
    }

    // Range analysis
    const ranges = models.map(m => m.range_epa_miles).filter(r => r !== null) as number[];
    if (ranges.length > 1) {
      const minRange = Math.min(...ranges);
      const maxRange = Math.max(...ranges);
      const rangeDiff = maxRange - minRange;
      const shortestRangeModel = models.find(m => m.range_epa_miles === minRange);
      const longestRangeModel = models.find(m => m.range_epa_miles === maxRange);

      if (rangeDiff > 50) {
        insights.push({
          type: 'range',
          icon: 'speedometer' as keyof typeof Ionicons.glyphMap,
          title: 'Range Considerations',
          description: `${longestRangeModel?.make} ${longestRangeModel?.model} offers ${rangeDiff} miles more range than ${shortestRangeModel?.make} ${shortestRangeModel?.model}`,
          recommendation: minRange < 250 ? 'Consider your daily driving needs - shorter range may require more frequent charging' : 'All vehicles offer adequate range for most users',
          color: 'bg-blue-50 border-blue-200 text-blue-800',
        });
      }
    }

    // Efficiency analysis
    const efficiencies = models.map(m => m.efficiency_mpge).filter(e => e !== null) as number[];
    if (efficiencies.length > 1) {
      const minEfficiency = Math.min(...efficiencies);
      const maxEfficiency = Math.max(...efficiencies);
      const efficiencyDiff = maxEfficiency - minEfficiency;
      const mostEfficientModel = models.find(m => m.efficiency_mpge === maxEfficiency);

      if (efficiencyDiff > 20) {
        insights.push({
          type: 'efficiency',
          icon: 'leaf' as keyof typeof Ionicons.glyphMap,
          title: 'Efficiency Impact',
          description: `${mostEfficientModel?.make} ${mostEfficientModel?.model} is ${efficiencyDiff} MPGe more efficient`,
          recommendation: 'Higher efficiency means lower electricity costs and better environmental impact',
          color: 'bg-green-50 border-green-200 text-green-800',
        });
      }
    }

    // Performance analysis
    const accelerations = models.map(m => m.acceleration_0_60_mph).filter(a => a !== null) as number[];
    if (accelerations.length > 1) {
      const fastestAcceleration = Math.min(...accelerations);
      const slowestAcceleration = Math.max(...accelerations);
      const accelerationDiff = slowestAcceleration - fastestAcceleration;
      const fastestModel = models.find(m => m.acceleration_0_60_mph === fastestAcceleration);

      if (accelerationDiff > 2) {
        insights.push({
          type: 'performance',
          icon: 'flash' as keyof typeof Ionicons.glyphMap,
          title: 'Performance Differences',
          description: `${fastestModel?.make} ${fastestModel?.model} accelerates ${accelerationDiff.toFixed(1)}s faster to 60 mph`,
          recommendation: fastestAcceleration < 4 ? 'Excellent performance for highway merging and overtaking' : 'All vehicles offer adequate performance for daily driving',
          color: 'bg-purple-50 border-purple-200 text-purple-800',
        });
      }
    }

    return insights;
  };

  const generateRecommendations = () => {
    if (models.length === 0) return [];

    const recommendations = [];

    // Best overall value
    const bestValueModel = models.reduce((best, current) => {
      const bestScore = calculateValueScore(best);
      const currentScore = calculateValueScore(current);
      return currentScore > bestScore ? current : best;
    });

    recommendations.push({
      type: 'best-value',
      title: 'Best Overall Value',
      model: bestValueModel,
      reason: 'Offers the best combination of price, range, and efficiency',
      icon: 'trophy' as keyof typeof Ionicons.glyphMap,
      color: 'bg-yellow-50 border-yellow-200',
    });

    // Most practical
    const mostPracticalModel = models.reduce((best, current) => {
      const bestPracticality = calculatePracticalityScore(best);
      const currentPracticality = calculatePracticalityScore(current);
      return currentPracticality > bestPracticality ? current : best;
    });

    if (mostPracticalModel.id !== bestValueModel.id) {
      recommendations.push({
        type: 'most-practical',
        title: 'Most Practical Choice',
        model: mostPracticalModel,
        reason: 'Best for daily driving with good range and charging capabilities',
        icon: 'car' as keyof typeof Ionicons.glyphMap,
        color: 'bg-blue-50 border-blue-200',
      });
    }

    // Budget option
    const budgetModel = models.reduce((cheapest, current) => {
      if (!current.price_msrp) return cheapest;
      if (!cheapest.price_msrp) return current;
      return current.price_msrp < cheapest.price_msrp ? current : cheapest;
    });

    if (budgetModel.price_msrp && budgetModel.id !== bestValueModel.id) {
      recommendations.push({
        type: 'budget',
        title: 'Budget-Friendly Option',
        model: budgetModel,
        reason: 'Lowest upfront cost while still offering good EV benefits',
        icon: 'cash' as keyof typeof Ionicons.glyphMap,
        color: 'bg-green-50 border-green-200',
      });
    }

    return recommendations;
  };

  const calculateValueScore = (model: EVModel): number => {
    let score = 0;
    let factors = 0;

    // Price score (lower is better)
    if (model.price_msrp) {
      score += Math.max(0, 100 - (model.price_msrp / 100000)); // Normalize around $100k
      factors++;
    }

    // Range score
    if (model.range_epa_miles) {
      score += Math.min(100, model.range_epa_miles / 4); // Normalize around 400 miles
      factors++;
    }

    // Efficiency score
    if (model.efficiency_mpge) {
      score += Math.min(100, model.efficiency_mpge / 1.5); // Normalize around 150 MPGe
      factors++;
    }

    return factors > 0 ? score / factors : 0;
  };

  const calculatePracticalityScore = (model: EVModel): number => {
    let score = 0;
    let factors = 0;

    // Range practicality
    if (model.range_epa_miles) {
      score += model.range_epa_miles > 250 ? 100 : (model.range_epa_miles / 250) * 100;
      factors++;
    }

    // Charging speed
    if (model.charging_speed_dc_kw) {
      score += model.charging_speed_dc_kw > 150 ? 100 : (model.charging_speed_dc_kw / 150) * 100;
      factors++;
    }

    // Seating capacity
    if (model.seating_capacity) {
      score += model.seating_capacity >= 5 ? 100 : (model.seating_capacity / 5) * 100;
      factors++;
    }

    return factors > 0 ? score / factors : 0;
  };

  const insights = generateInsights();
  const recommendations = generateRecommendations();

  const InsightCard = ({ insight }: { insight: any }) => (
    <View className={`bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm border ${insight.color.split(' ')[1]}`}>
      <View className="flex-row items-start">
        <View className={`p-2 rounded-lg ${insight.color.split(' ')[0]} mr-3`}>
          <Ionicons name={insight.icon} size={20} color={insight.color.split(' ')[2].replace('text-', '#')} />
        </View>
        <View className="flex-1">
          <Text className="text-lg font-semibold text-gray-900 mb-2">{insight.title}</Text>
          <Text className="text-gray-700 mb-3">{insight.description}</Text>
          <View className={`${insight.color.split(' ')[0]} rounded-lg p-3`}>
            <Text className={`text-sm font-medium ${insight.color.split(' ')[2]}`}>
              💡 {insight.recommendation}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const RecommendationCard = ({ recommendation }: { recommendation: any }) => (
    <TouchableOpacity
      onPress={() => router.push(`/ev-models/${recommendation.model.id}`)}
      className={`bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm border ${recommendation.color.split(' ')[1]}`}
    >
      <View className="flex-row items-center mb-3">
        <View className={`p-2 rounded-lg ${recommendation.color.split(' ')[0]} mr-3`}>
          <Ionicons name={recommendation.icon} size={20} color="#6b7280" />
        </View>
        <Text className="text-lg font-semibold text-gray-900">{recommendation.title}</Text>
      </View>
      
      <View className="ml-11">
        <Text className="text-xl font-bold text-gray-900 mb-1">
          {recommendation.model.make} {recommendation.model.model}
        </Text>
        {recommendation.model.trim && (
          <Text className="text-sm text-gray-600 mb-2">{recommendation.model.trim}</Text>
        )}
        <Text className="text-lg font-bold text-electric-600 mb-3">
          {formatPrice(recommendation.model.price_msrp)}
        </Text>
        <Text className="text-gray-700 mb-3">{recommendation.reason}</Text>
        
        <View className="flex-row justify-between">
          <View className="items-center">
            <Text className="text-xs text-gray-500">Range</Text>
            <Text className="font-semibold text-gray-900">
              {formatRange(recommendation.model.range_epa_miles)}
            </Text>
          </View>
          {recommendation.model.efficiency_mpge && (
            <View className="items-center">
              <Text className="text-xs text-gray-500">Efficiency</Text>
              <Text className="font-semibold text-gray-900">
                {recommendation.model.efficiency_mpge} MPGe
              </Text>
            </View>
          )}
          {recommendation.model.acceleration_0_60_mph && (
            <View className="items-center">
              <Text className="text-xs text-gray-500">0-60 mph</Text>
              <Text className="font-semibold text-gray-900">
                {recommendation.model.acceleration_0_60_mph}s
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* AI Insights */}
      <View className="p-4">
        <Text className="text-lg font-semibold text-gray-900 mb-4">AI-Powered Insights</Text>
        
        {insights.length > 0 ? (
          insights.map((insight, index) => (
            <InsightCard key={index} insight={insight} />
          ))
        ) : (
          <View className="bg-white rounded-xl p-6 shadow-sm">
            <View className="items-center">
              <Ionicons name="bulb-outline" size={48} color="#6b7280" />
              <Text className="text-gray-600 text-center mt-4">
                Add more vehicles to get detailed insights and comparisons
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Recommendations */}
      <View className="pb-20">
        <View className="px-4 mb-4">
          <Text className="text-lg font-semibold text-gray-900">Our Recommendations</Text>
          <Text className="text-gray-600 text-sm">
            Based on analysis of your comparison vehicles
          </Text>
        </View>
        
        {recommendations.map((recommendation, index) => (
          <RecommendationCard key={index} recommendation={recommendation} />
        ))}
      </View>

      {/* Decision Tips */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Decision-Making Tips</Text>
        
        <View className="space-y-3">
          <View className="flex-row items-start">
            <Ionicons name="checkmark-circle" size={20} color="#22c55e" className="mt-1" />
            <Text className="text-gray-700 ml-3 flex-1">
              Consider your daily driving patterns - most people drive less than 50 miles per day
            </Text>
          </View>
          
          <View className="flex-row items-start">
            <Ionicons name="checkmark-circle" size={20} color="#22c55e" className="mt-1" />
            <Text className="text-gray-700 ml-3 flex-1">
              Factor in available charging infrastructure in your area
            </Text>
          </View>
          
          <View className="flex-row items-start">
            <Ionicons name="checkmark-circle" size={20} color="#22c55e" className="mt-1" />
            <Text className="text-gray-700 ml-3 flex-1">
              Don't forget about federal and state incentives that can reduce the effective price
            </Text>
          </View>
          
          <View className="flex-row items-start">
            <Ionicons name="checkmark-circle" size={20} color="#22c55e" className="mt-1" />
            <Text className="text-gray-700 ml-3 flex-1">
              Test drive your top choices to experience the differences firsthand
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
