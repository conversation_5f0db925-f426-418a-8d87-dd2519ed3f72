-- EV Buyer's Guide Database Schema
-- Migration: 001_create_ev_buyer_guide_tables.sql
-- Description: Create tables for EV model database and buyer decision tools

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create EV Manufacturers table
CREATE TABLE IF NOT EXISTS ev_manufacturers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL UNIQUE,
  logo_url TEXT,
  website_url TEXT,
  headquarters_country VARCHAR(50),
  founded_year INTEGER,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create EV Models table (buyer-focused)
CREATE TABLE IF NOT EXISTS ev_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  manufacturer_id UUID REFERENCES ev_manufacturers(id) ON DELETE CASCADE,
  make VARCHAR(50) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL,
  trim VARCHAR(100),
  body_type VARCHAR(50), -- sedan, suv, hatchback, truck, coupe, wagon, convertible
  
  -- Pricing Information (buyer-focused)
  price_msrp INTEGER, -- MSRP in cents (when available)
  price_base INTEGER, -- Base model price in cents
  price_as_tested INTEGER, -- As-tested price in cents
  production_status VARCHAR(20) DEFAULT 'current', -- current, discontinued, concept, upcoming
  market_regions TEXT[], -- regions where this model is/was available
  
  -- Technical Specifications (buyer decision factors)
  battery_capacity_kwh DECIMAL(5,2) NOT NULL,
  range_epa_miles INTEGER,
  range_wltp_miles INTEGER,
  range_real_world_miles INTEGER, -- real-world range estimates
  efficiency_mpge INTEGER,
  charging_speed_dc_kw INTEGER,
  charging_speed_ac_kw INTEGER,
  charging_ports TEXT[], -- array of connector types
  charging_time_10_80_minutes INTEGER, -- practical charging time
  
  -- Performance (user-relevant)
  acceleration_0_60_mph DECIMAL(3,1),
  top_speed_mph INTEGER,
  motor_power_hp INTEGER,
  motor_torque_lb_ft INTEGER,
  drivetrain VARCHAR(20), -- fwd, rwd, awd
  
  -- Physical Specifications
  length_inches DECIMAL(5,1),
  width_inches DECIMAL(5,1),
  height_inches DECIMAL(5,1),
  wheelbase_inches DECIMAL(5,1),
  ground_clearance_inches DECIMAL(3,1),
  cargo_volume_cubic_ft DECIMAL(4,1),
  seating_capacity INTEGER,
  curb_weight_lbs INTEGER,
  
  -- Features & Technology (buyer decision factors)
  features JSONB, -- structured feature data
  safety_ratings JSONB, -- NHTSA, IIHS ratings
  warranty_info JSONB,
  total_cost_ownership JSONB, -- estimated costs, incentives, savings
  user_reviews_summary JSONB, -- aggregated user feedback
  pros_cons JSONB, -- structured pros and cons for buyers
  
  -- Media & Content
  images TEXT[], -- array of image URLs
  videos TEXT[], -- array of video URLs
  brochure_url TEXT,
  
  -- Buyer Decision Support
  is_featured BOOLEAN DEFAULT false,
  popularity_score INTEGER DEFAULT 0,
  editor_choice BOOLEAN DEFAULT false,
  best_value BOOLEAN DEFAULT false,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(make, model, year, trim)
);

-- Create User Favorites table
CREATE TABLE IF NOT EXISTS user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  ev_model_id UUID REFERENCES ev_models(id) ON DELETE CASCADE,
  notes TEXT, -- user's personal notes about this model
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, ev_model_id)
);

-- Create Comparison Sessions table
CREATE TABLE IF NOT EXISTS comparison_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name VARCHAR(100),
  ev_model_ids UUID[],
  user_priorities JSONB, -- user's weighted priorities (price, range, features, etc.)
  comparison_notes TEXT,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create User Preferences table (for decision wizard)
CREATE TABLE IF NOT EXISTS user_ev_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  budget_min INTEGER, -- minimum budget in cents
  budget_max INTEGER, -- maximum budget in cents
  range_requirement_miles INTEGER, -- minimum range needed
  body_type_preferences TEXT[], -- preferred body types
  use_case VARCHAR(50), -- commuting, family, performance, etc.
  charging_at_home BOOLEAN,
  daily_driving_miles INTEGER,
  priority_weights JSONB, -- weighted importance of factors
  lifestyle_data JSONB, -- additional lifestyle information
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create indexes for performance optimization
-- Search and filtering indexes
CREATE INDEX IF NOT EXISTS idx_ev_models_make_model ON ev_models(make, model);
CREATE INDEX IF NOT EXISTS idx_ev_models_year ON ev_models(year);
CREATE INDEX IF NOT EXISTS idx_ev_models_price ON ev_models(price_msrp);
CREATE INDEX IF NOT EXISTS idx_ev_models_range ON ev_models(range_epa_miles);
CREATE INDEX IF NOT EXISTS idx_ev_models_body_type ON ev_models(body_type);
CREATE INDEX IF NOT EXISTS idx_ev_models_production_status ON ev_models(production_status);
CREATE INDEX IF NOT EXISTS idx_ev_models_featured ON ev_models(is_featured);
CREATE INDEX IF NOT EXISTS idx_ev_models_manufacturer ON ev_models(manufacturer_id);

-- Full-text search index
CREATE INDEX IF NOT EXISTS idx_ev_models_search ON ev_models 
USING gin(to_tsvector('english', make || ' ' || model || ' ' || COALESCE(trim, '')));

-- User-specific indexes
CREATE INDEX IF NOT EXISTS idx_user_favorites_user ON user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_comparison_sessions_user ON comparison_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ev_preferences_user ON user_ev_preferences(user_id);

-- Manufacturer index
CREATE INDEX IF NOT EXISTS idx_ev_manufacturers_name ON ev_manufacturers(name);
CREATE INDEX IF NOT EXISTS idx_ev_manufacturers_active ON ev_manufacturers(is_active);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER ev_manufacturers_updated_at
  BEFORE UPDATE ON ev_manufacturers
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER ev_models_updated_at
  BEFORE UPDATE ON ev_models
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER comparison_sessions_updated_at
  BEFORE UPDATE ON comparison_sessions
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER user_ev_preferences_updated_at
  BEFORE UPDATE ON user_ev_preferences
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();
