import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface QuickFilter {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  filters: Record<string, any>;
  description: string;
}

interface QuickFiltersProps {
  onFilterSelect: (filters: Record<string, any>) => void;
  activeFilters: Record<string, any>;
}

const quickFilters: QuickFilter[] = [
  {
    id: 'affordable',
    label: 'Under $40k',
    icon: 'cash',
    filters: { priceMax: '4000000' }, // $40k in cents
    description: 'Budget-friendly EVs',
  },
  {
    id: 'long-range',
    label: '300+ Miles',
    icon: 'speedometer',
    filters: { rangeMin: '300' },
    description: 'Long-range vehicles',
  },
  {
    id: 'fast-charging',
    label: 'Fast Charging',
    icon: 'flash',
    filters: { chargingSpeedMin: '150' }, // 150kW+
    description: 'Quick charging capability',
  },
  {
    id: 'family',
    label: 'Family SUVs',
    icon: 'car',
    filters: { bodyType: 'suv', seatingMin: '5' },
    description: 'Spacious family vehicles',
  },
  {
    id: 'luxury',
    label: 'Luxury',
    icon: 'diamond',
    filters: { priceMin: '7000000' }, // $70k+ in cents
    description: 'Premium vehicles',
  },
  {
    id: 'efficient',
    label: 'Most Efficient',
    icon: 'leaf',
    filters: { efficiencyMin: '120' }, // 120+ MPGe
    description: 'Highest efficiency',
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: 'rocket',
    filters: { accelerationMax: '4.0' }, // Under 4.0s 0-60
    description: 'High-performance EVs',
  },
  {
    id: 'featured',
    label: 'Featured',
    icon: 'star',
    filters: { featured: 'true' },
    description: 'Editor picks',
  },
];

export function QuickFilters({ onFilterSelect, activeFilters }: QuickFiltersProps) {
  const isFilterActive = (filter: QuickFilter) => {
    return Object.entries(filter.filters).some(([key, value]) => {
      return activeFilters[key] === value;
    });
  };

  const handleFilterPress = (filter: QuickFilter) => {
    if (isFilterActive(filter)) {
      // Remove filter by setting to undefined
      const clearedFilters = Object.keys(filter.filters).reduce((acc, key) => {
        acc[key] = undefined;
        return acc;
      }, {} as Record<string, any>);
      onFilterSelect(clearedFilters);
    } else {
      // Apply filter
      onFilterSelect(filter.filters);
    }
  };

  return (
    <View className="bg-white border-b border-gray-200">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingVertical: 12,
          gap: 8,
        }}
      >
        {quickFilters.map((filter) => {
          const isActive = isFilterActive(filter);
          
          return (
            <TouchableOpacity
              key={filter.id}
              onPress={() => handleFilterPress(filter)}
              className={`flex-row items-center px-4 py-2 rounded-full border ${
                isActive
                  ? 'bg-electric-600 border-electric-600'
                  : 'bg-white border-gray-300'
              }`}
            >
              <Ionicons
                name={filter.icon}
                size={16}
                color={isActive ? 'white' : '#6b7280'}
              />
              <Text
                className={`ml-2 font-medium ${
                  isActive ? 'text-white' : 'text-gray-700'
                }`}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
}
