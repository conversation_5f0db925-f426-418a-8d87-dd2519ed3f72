import React from 'react'
import { View, Text, TouchableOpacity, Image, Dimensions } from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { EVModel } from '@greenmiles-ev/shared'

interface EVModelCardProps {
  model: EVModel
  viewMode: 'grid' | 'list'
  onPress: () => void
  onCompareToggle: () => void
  isInComparison: boolean
}

const { width: screenWidth } = Dimensions.get('window')
const cardWidth = (screenWidth - 48) / 2 // Account for padding and gap

export function EVModelCard({
  model,
  viewMode,
  onPress,
  onCompareToggle,
  isInComparison,
}: EVModelCardProps) {
  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA'
    return `$${(priceInCents / 100).toLocaleString()}`
  }

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A'
    return `${range} mi`
  }

  const getImageSource = () => {
    if (model.images && model.images.length > 0) {
      return { uri: model.images[0] }
    }
    // Fallback to a solid color background for now
    return null
  }

  const getBadges = () => {
    const badges = []
    if (model.is_featured) badges.push({ text: 'Featured', color: 'bg-electric-600' })
    if (model.editor_choice) badges.push({ text: "Editor's Choice", color: 'bg-blue-600' })
    if (model.best_value) badges.push({ text: 'Best Value', color: 'bg-green-600' })
    return badges
  }

  const getEfficiencyRating = () => {
    if (!model.efficiency_mpge) return null
    if (model.efficiency_mpge >= 130) return { rating: 'Excellent', color: 'text-green-600' }
    if (model.efficiency_mpge >= 110) return { rating: 'Very Good', color: 'text-blue-600' }
    if (model.efficiency_mpge >= 90) return { rating: 'Good', color: 'text-yellow-600' }
    return { rating: 'Average', color: 'text-gray-600' }
  }

  if (viewMode === 'list') {
    return (
      <TouchableOpacity
        onPress={onPress}
        className="bg-white mx-4 mb-3 rounded-xl shadow-sm border border-gray-100"
      >
        <View className="flex-row p-4">
          {/* Image */}
          <View className="relative">
            {getImageSource() ? (
              <Image
                source={getImageSource()}
                className="w-24 h-16 rounded-lg"
                resizeMode="cover"
              />
            ) : (
              <View className="w-24 h-16 rounded-lg bg-gray-200 items-center justify-center">
                <Ionicons name="car" size={24} color="#9ca3af" />
              </View>
            )}
            {getBadges().length > 0 && (
              <View className="absolute -top-1 -right-1">
                <View className={`${getBadges()[0].color} rounded-full px-2 py-1`}>
                  <Text className="text-white text-xs font-medium">{getBadges()[0].text}</Text>
                </View>
              </View>
            )}
          </View>

          {/* Content */}
          <View className="flex-1 ml-4">
            <View className="flex-row items-start justify-between">
              <View className="flex-1">
                <Text className="text-lg font-semibold text-gray-900" numberOfLines={1}>
                  {model.make} {model.model}
                </Text>
                {model.trim && (
                  <Text className="text-sm text-gray-600" numberOfLines={1}>
                    {model.trim}
                  </Text>
                )}
                <Text className="text-lg font-bold text-electric-600 mt-1">
                  {formatPrice(model.price_msrp)}
                </Text>
              </View>

              {/* Compare Button */}
              <TouchableOpacity
                onPress={onCompareToggle}
                className={`p-2 rounded-lg ${isInComparison ? 'bg-electric-600' : 'bg-gray-100'}`}
              >
                <Ionicons name="analytics" size={18} color={isInComparison ? 'white' : '#6b7280'} />
              </TouchableOpacity>
            </View>

            {/* Specs */}
            <View className="flex-row items-center mt-3 space-x-4">
              <View className="flex-row items-center">
                <Ionicons name="speedometer" size={14} color="#6b7280" />
                <Text className="text-sm text-gray-600 ml-1">
                  {formatRange(model.range_epa_miles)}
                </Text>
              </View>
              {model.efficiency_mpge && (
                <View className="flex-row items-center">
                  <Ionicons name="leaf" size={14} color="#6b7280" />
                  <Text className="text-sm text-gray-600 ml-1">{model.efficiency_mpge} MPGe</Text>
                </View>
              )}
              {model.acceleration_0_60_mph && (
                <View className="flex-row items-center">
                  <Ionicons name="flash" size={14} color="#6b7280" />
                  <Text className="text-sm text-gray-600 ml-1">{model.acceleration_0_60_mph}s</Text>
                </View>
              )}
            </View>

            {/* Efficiency Rating */}
            {getEfficiencyRating() && (
              <View className="mt-2">
                <Text className={`text-xs font-medium ${getEfficiencyRating()?.color}`}>
                  {getEfficiencyRating()?.rating} Efficiency
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    )
  }

  // Grid view
  return (
    <TouchableOpacity
      onPress={onPress}
      className="bg-white rounded-xl shadow-sm border border-gray-100 mb-3"
      style={{ width: cardWidth }}
    >
      {/* Image */}
      <View className="relative">
        {getImageSource() ? (
          <Image
            source={getImageSource()}
            className="w-full h-32 rounded-t-xl"
            resizeMode="cover"
          />
        ) : (
          <View className="w-full h-32 rounded-t-xl bg-gray-200 items-center justify-center">
            <Ionicons name="car" size={32} color="#9ca3af" />
          </View>
        )}

        {/* Badges */}
        {getBadges().length > 0 && (
          <View className="absolute top-2 left-2">
            <View className={`${getBadges()[0].color} rounded-full px-2 py-1`}>
              <Text className="text-white text-xs font-medium">{getBadges()[0].text}</Text>
            </View>
          </View>
        )}

        {/* Compare Button */}
        <TouchableOpacity
          onPress={onCompareToggle}
          className={`absolute top-2 right-2 p-2 rounded-lg ${
            isInComparison ? 'bg-electric-600' : 'bg-white/90'
          }`}
        >
          <Ionicons name="analytics" size={16} color={isInComparison ? 'white' : '#6b7280'} />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View className="p-3">
        <Text className="text-base font-semibold text-gray-900" numberOfLines={1}>
          {model.make} {model.model}
        </Text>
        {model.trim && (
          <Text className="text-sm text-gray-600" numberOfLines={1}>
            {model.trim}
          </Text>
        )}

        <Text className="text-lg font-bold text-electric-600 mt-1">
          {formatPrice(model.price_msrp)}
        </Text>

        {/* Key Specs */}
        <View className="mt-3 space-y-2">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons name="speedometer" size={12} color="#6b7280" />
              <Text className="text-xs text-gray-600 ml-1">Range</Text>
            </View>
            <Text className="text-xs font-medium text-gray-900">
              {formatRange(model.range_epa_miles)}
            </Text>
          </View>

          {model.efficiency_mpge && (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="leaf" size={12} color="#6b7280" />
                <Text className="text-xs text-gray-600 ml-1">Efficiency</Text>
              </View>
              <Text className="text-xs font-medium text-gray-900">
                {model.efficiency_mpge} MPGe
              </Text>
            </View>
          )}

          {model.acceleration_0_60_mph && (
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Ionicons name="flash" size={12} color="#6b7280" />
                <Text className="text-xs text-gray-600 ml-1">0-60 mph</Text>
              </View>
              <Text className="text-xs font-medium text-gray-900">
                {model.acceleration_0_60_mph}s
              </Text>
            </View>
          )}
        </View>

        {/* Efficiency Rating */}
        {getEfficiencyRating() && (
          <View className="mt-2 pt-2 border-t border-gray-100">
            <Text className={`text-xs font-medium ${getEfficiencyRating()?.color}`}>
              {getEfficiencyRating()?.rating} Efficiency
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}
