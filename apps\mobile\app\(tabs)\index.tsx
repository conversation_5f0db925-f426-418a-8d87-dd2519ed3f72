import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function DashboardScreen() {
  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Header Stats */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-6 shadow-sm">
        <Text className="text-2xl font-bold text-gray-900 mb-2">Welcome back!</Text>
        <Text className="text-gray-600 mb-4">Here's your EV summary</Text>
        
        <View className="flex-row justify-between">
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">85%</Text>
            <Text className="text-sm text-gray-600">Battery</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">247</Text>
            <Text className="text-sm text-gray-600">Miles Range</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-electric-600">$42</Text>
            <Text className="text-sm text-gray-600">This Month</Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</Text>
        <View className="flex-row justify-between">
          <TouchableOpacity className="bg-white rounded-xl p-4 flex-1 mr-2 shadow-sm items-center">
            <Ionicons name="map-outline" size={24} color="#22c55e" />
            <Text className="text-sm font-medium text-gray-900 mt-2">Find Charging</Text>
          </TouchableOpacity>
          
          <TouchableOpacity className="bg-white rounded-xl p-4 flex-1 mx-1 shadow-sm items-center">
            <Ionicons name="car-outline" size={24} color="#22c55e" />
            <Text className="text-sm font-medium text-gray-900 mt-2">My Vehicle</Text>
          </TouchableOpacity>
          
          <TouchableOpacity className="bg-white rounded-xl p-4 flex-1 ml-2 shadow-sm items-center">
            <Ionicons name="analytics-outline" size={24} color="#22c55e" />
            <Text className="text-sm font-medium text-gray-900 mt-2">Analytics</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recent Activity */}
      <View className="mx-4 mt-6">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Recent Activity</Text>
        <View className="bg-white rounded-xl shadow-sm">
          <View className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <View className="bg-electric-100 rounded-full p-2 mr-3">
                <Ionicons name="battery-charging" size={16} color="#22c55e" />
              </View>
              <View className="flex-1">
                <Text className="font-medium text-gray-900">Charging Complete</Text>
                <Text className="text-sm text-gray-600">Tesla Supercharger - Downtown</Text>
              </View>
              <Text className="text-sm text-gray-500">2h ago</Text>
            </View>
          </View>
          
          <View className="p-4 border-b border-gray-100">
            <View className="flex-row items-center">
              <View className="bg-blue-100 rounded-full p-2 mr-3">
                <Ionicons name="car" size={16} color="#3b82f6" />
              </View>
              <View className="flex-1">
                <Text className="font-medium text-gray-900">Trip Completed</Text>
                <Text className="text-sm text-gray-600">Home to Office - 24.5 miles</Text>
              </View>
              <Text className="text-sm text-gray-500">5h ago</Text>
            </View>
          </View>
          
          <View className="p-4">
            <View className="flex-row items-center">
              <View className="bg-green-100 rounded-full p-2 mr-3">
                <Ionicons name="leaf" size={16} color="#10b981" />
              </View>
              <View className="flex-1">
                <Text className="font-medium text-gray-900">Carbon Saved</Text>
                <Text className="text-sm text-gray-600">12.3 lbs CO₂ this week</Text>
              </View>
              <Text className="text-sm text-gray-500">1d ago</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Bottom Spacing */}
      <View className="h-6" />
    </ScrollView>
  );
}
