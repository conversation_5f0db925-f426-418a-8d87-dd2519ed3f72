import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { EVModel } from '@greenmiles-ev/shared';
import { EVModelCard } from './EVModelCard';

interface EVDetailsOverviewProps {
  model: EVModel;
  similarModels: EVModel[];
}

export function EVDetailsOverview({ model, similarModels }: EVDetailsOverviewProps) {
  const formatPrice = (priceInCents: number | null) => {
    if (!priceInCents) return 'Price TBA';
    return `$${(priceInCents / 100).toLocaleString()}`;
  };

  const formatRange = (range: number | null) => {
    if (!range) return 'N/A';
    return `${range} mi`;
  };

  const getEfficiencyRating = () => {
    if (!model.efficiency_mpge) return null;
    if (model.efficiency_mpge >= 130) return { rating: 'Excellent', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (model.efficiency_mpge >= 110) return { rating: 'Very Good', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (model.efficiency_mpge >= 90) return { rating: 'Good', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { rating: 'Average', color: 'text-gray-600', bgColor: 'bg-gray-100' };
  };

  const getValueProposition = () => {
    const propositions = [];
    if (model.best_value) propositions.push('Best Value');
    if (model.editor_choice) propositions.push('Editor\'s Choice');
    if (model.is_featured) propositions.push('Featured');
    
    // Calculate value metrics
    const pricePerMile = model.price_msrp && model.range_epa_miles 
      ? (model.price_msrp / 100) / model.range_epa_miles 
      : null;
    
    if (pricePerMile && pricePerMile < 200) propositions.push('Great Range Value');
    if (model.efficiency_mpge && model.efficiency_mpge > 120) propositions.push('Highly Efficient');
    
    return propositions;
  };

  const getProsCons = () => {
    const pros = [];
    const cons = [];

    // Generate pros based on specs
    if (model.range_epa_miles && model.range_epa_miles > 300) pros.push('Long driving range');
    if (model.efficiency_mpge && model.efficiency_mpge > 120) pros.push('Excellent efficiency');
    if (model.acceleration_0_60_mph && model.acceleration_0_60_mph < 4.0) pros.push('Quick acceleration');
    if (model.charging_speed_dc_kw && model.charging_speed_dc_kw > 150) pros.push('Fast charging capability');
    if (model.seating_capacity && model.seating_capacity >= 7) pros.push('Spacious seating');
    if (model.best_value) pros.push('Great value for money');

    // Generate cons based on specs
    if (model.price_msrp && model.price_msrp > 8000000) cons.push('Premium pricing');
    if (model.range_epa_miles && model.range_epa_miles < 200) cons.push('Limited range');
    if (model.acceleration_0_60_mph && model.acceleration_0_60_mph > 8.0) cons.push('Slower acceleration');
    if (model.charging_speed_dc_kw && model.charging_speed_dc_kw < 100) cons.push('Slower charging');

    return { pros, cons };
  };

  const { pros, cons } = getProsCons();
  const efficiencyRating = getEfficiencyRating();
  const valueProps = getValueProposition();

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Key Highlights */}
      <View className="bg-white m-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Key Highlights</Text>
        
        <View className="space-y-3">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <Ionicons name="speedometer" size={20} color="#6b7280" />
              <Text className="text-gray-700 ml-2">EPA Range</Text>
            </View>
            <Text className="font-semibold text-gray-900">
              {formatRange(model.range_epa_miles)}
            </Text>
          </View>

          {model.efficiency_mpge && (
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Ionicons name="leaf" size={20} color="#6b7280" />
                <Text className="text-gray-700 ml-2">Efficiency</Text>
              </View>
              <View className="flex-row items-center">
                <Text className="font-semibold text-gray-900 mr-2">
                  {model.efficiency_mpge} MPGe
                </Text>
                {efficiencyRating && (
                  <View className={`px-2 py-1 rounded-full ${efficiencyRating.bgColor}`}>
                    <Text className={`text-xs font-medium ${efficiencyRating.color}`}>
                      {efficiencyRating.rating}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {model.acceleration_0_60_mph && (
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Ionicons name="flash" size={20} color="#6b7280" />
                <Text className="text-gray-700 ml-2">0-60 mph</Text>
              </View>
              <Text className="font-semibold text-gray-900">
                {model.acceleration_0_60_mph}s
              </Text>
            </View>
          )}

          {model.charging_speed_dc_kw && (
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Ionicons name="flash-outline" size={20} color="#6b7280" />
                <Text className="text-gray-700 ml-2">DC Fast Charging</Text>
              </View>
              <Text className="font-semibold text-gray-900">
                {model.charging_speed_dc_kw} kW
              </Text>
            </View>
          )}

          {model.seating_capacity && (
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Ionicons name="people" size={20} color="#6b7280" />
                <Text className="text-gray-700 ml-2">Seating</Text>
              </View>
              <Text className="font-semibold text-gray-900">
                {model.seating_capacity} seats
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Value Propositions */}
      {valueProps.length > 0 && (
        <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
          <Text className="text-lg font-semibold text-gray-900 mb-3">Why This Vehicle</Text>
          <View className="flex-row flex-wrap gap-2">
            {valueProps.map((prop, index) => (
              <View key={index} className="bg-electric-100 rounded-full px-3 py-1">
                <Text className="text-electric-700 text-sm font-medium">{prop}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Pros and Cons */}
      {(pros.length > 0 || cons.length > 0) && (
        <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
          <Text className="text-lg font-semibold text-gray-900 mb-4">Pros & Cons</Text>
          
          <View className="space-y-4">
            {pros.length > 0 && (
              <View>
                <Text className="text-green-600 font-medium mb-2">Pros</Text>
                {pros.map((pro, index) => (
                  <View key={index} className="flex-row items-center mb-1">
                    <Ionicons name="checkmark-circle" size={16} color="#22c55e" />
                    <Text className="text-gray-700 ml-2">{pro}</Text>
                  </View>
                ))}
              </View>
            )}

            {cons.length > 0 && (
              <View>
                <Text className="text-orange-600 font-medium mb-2">Considerations</Text>
                {cons.map((con, index) => (
                  <View key={index} className="flex-row items-center mb-1">
                    <Ionicons name="information-circle" size={16} color="#f59e0b" />
                    <Text className="text-gray-700 ml-2">{con}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>
      )}

      {/* Ideal For */}
      <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
        <Text className="text-lg font-semibold text-gray-900 mb-3">Ideal For</Text>
        <View className="space-y-2">
          {model.body_type === 'suv' && (
            <View className="flex-row items-center">
              <Ionicons name="people" size={16} color="#6b7280" />
              <Text className="text-gray-700 ml-2">Families needing space and versatility</Text>
            </View>
          )}
          {model.range_epa_miles && model.range_epa_miles > 300 && (
            <View className="flex-row items-center">
              <Ionicons name="car" size={16} color="#6b7280" />
              <Text className="text-gray-700 ml-2">Long-distance commuters and road trips</Text>
            </View>
          )}
          {model.efficiency_mpge && model.efficiency_mpge > 120 && (
            <View className="flex-row items-center">
              <Ionicons name="leaf" size={16} color="#6b7280" />
              <Text className="text-gray-700 ml-2">Eco-conscious drivers</Text>
            </View>
          )}
          {model.acceleration_0_60_mph && model.acceleration_0_60_mph < 5.0 && (
            <View className="flex-row items-center">
              <Ionicons name="flash" size={16} color="#6b7280" />
              <Text className="text-gray-700 ml-2">Performance enthusiasts</Text>
            </View>
          )}
        </View>
      </View>

      {/* Similar Vehicles */}
      {similarModels.length > 0 && (
        <View className="bg-white mx-4 mb-4 rounded-xl p-4 shadow-sm">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-lg font-semibold text-gray-900">Similar Vehicles</Text>
            <TouchableOpacity onPress={() => router.push('/(tabs)/browse')}>
              <Text className="text-electric-600 font-medium">View All</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row gap-3">
              {similarModels.map((similarModel) => (
                <View key={similarModel.id} style={{ width: 200 }}>
                  <EVModelCard
                    model={similarModel}
                    viewMode="grid"
                    onPress={() => router.push(`/ev-models/${similarModel.id}`)}
                    onCompareToggle={() => {}}
                    isInComparison={false}
                  />
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      )}

      {/* Bottom Padding for Floating Buttons */}
      <View className="h-20" />
    </ScrollView>
  );
}
