'use client'

import { useState, useEffect, useCallback } from 'react'
import type { EVModel, EVModelFilters, SortOptions, EVModelsResponse } from '@/shared/types'

interface UseEVModelsParams {
  filters: EVModelFilters
  sortOptions: SortOptions
  searchQuery: string
  page?: number
  limit?: number
}

interface UseEVModelsReturn {
  evModels: EVModel[] | null
  loading: boolean
  error: string | null
  pagination: EVModelsResponse['pagination'] | null
  refetch: () => void
}

export function useEVModels({
  filters,
  sortOptions,
  searchQuery,
  page = 1,
  limit = 20
}: UseEVModelsParams): UseEVModelsReturn {
  const [evModels, setEvModels] = useState<EVModel[] | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<EVModelsResponse['pagination'] | null>(null)

  const fetchEVModels = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sort_by: sortOptions.field,
        sort_order: sortOptions.order
      })

      // Add filters
      if (filters.make) params.append('make', filters.make)
      if (filters.bodyType) params.append('body_type', filters.bodyType)
      if (filters.priceMin) params.append('price_min', filters.priceMin)
      if (filters.priceMax) params.append('price_max', filters.priceMax)
      if (filters.rangeMin) params.append('range_min', filters.rangeMin)
      if (filters.productionStatus) params.append('production_status', filters.productionStatus)
      if (filters.featured) params.append('featured', filters.featured)
      if (filters.bestValue) params.append('best_value', filters.bestValue)
      if (searchQuery) params.append('search', searchQuery)

      const response = await fetch(`/api/ev-models?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch EV models: ${response.status}`)
      }

      const data: EVModelsResponse = await response.json()
      
      setEvModels(data.data)
      setPagination(data.pagination)
    } catch (err) {
      console.error('Error fetching EV models:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch EV models')
      setEvModels(null)
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [filters, sortOptions, searchQuery, page, limit])

  useEffect(() => {
    fetchEVModels()
  }, [fetchEVModels])

  return {
    evModels,
    loading,
    error,
    pagination,
    refetch: fetchEVModels
  }
}
